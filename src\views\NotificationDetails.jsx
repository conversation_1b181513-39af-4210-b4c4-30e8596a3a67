import { useTranslation } from "react-i18next";
import '../languages/i18next';

//Fixed
import React, { useState } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,
    ScrollView,
    StatusBar
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import { screenHeight, screenWidth, bold, regular, f_25, f_s } from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import { SafeAreaView } from "react-native-safe-area-context";
import DropShadow from "react-native-drop-shadow";

const NotificationDetails = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const route = useRoute();
    const [data, setData] = useState(route.params.data);

    const go_back = () => {
        navigation.goBack();
    }

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content"/>
            <View style={[styles.header]}>
                <TouchableOpacity activeOpacity={1} onPress={go_back.bind(this)} style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                    <Icon type={Icons.MaterialIcons} name="arrow-back" color={colors.theme_fg_two} style={{ fontSize: 30 }} />
                </TouchableOpacity>
            </View>
            <ScrollView style={{paddingHorizontal:10}}>
                <DropShadow style={{width: "100%",  marginBottom: 5,shadowColor: "#000",shadowOffset: {width: 0,height: 0,},shadowOpacity: 0.1,shadowRadius: 2,}}>
                <View style={{ backgroundColor: colors.theme_bg_three, padding: 10, margin: 10, borderRadius: 10 }}>
                    <View style={{ margin: 10 }}>
                        <Text ellipsizeMode='tail' style={{ color: colors.theme_fg_two, fontSize: f_25, fontFamily: regular }}>{data.title}</Text>
                        <View style={{ margin: 10 }} />
                        <Text style={{ color: colors.grey, fontSize: f_s, fontFamily: regular }}>
                            {data.message}
                        </Text>
                    </View>
                    <View style={{ margin: 10 }} />
                </View>
                </DropShadow>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        ...StyleSheet.absoluteFillObject,
        height: screenHeight,
        width: screenWidth,
        backgroundColor: colors.theme
    },
    header: {
        height: 60,
        backgroundColor: colors.lite_bg,
        flexDirection: 'row',
        alignItems: 'center'
    },
});

export default NotificationDetails;