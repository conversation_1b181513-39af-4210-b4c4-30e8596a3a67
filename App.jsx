import { useTranslation } from "react-i18next";
import i18n, { i18nInitPromise } from './src/languages/i18next'; // adjust the path
import React, {  useEffect, useState } from 'react';
import { CommonActions, NavigationContainer, useNavigation } from '@react-navigation/native';
import { createDrawerNavigator, DrawerContentScrollView } from '@react-navigation/drawer';
import { StyleSheet, Text, TouchableOpacity, View, Image, Alert, Modal } from 'react-native'
import Icon, { Icons } from './src/components/Icons';
import * as colors from './src/assets/css/Colors';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import { screenWidth, bold, normal, regular, logo, img_url, api_url, get_profile, profile, delete_account_request, f_s, f_l, f_m } from './src/config/Constants';
import { connect } from 'react-redux';
import DropShadow from "react-native-drop-shadow";
import {createNativeStackNavigator} from '@react-navigation/native-stack';

import ReactNativeHapticFeedback from "react-native-haptic-feedback";
 const options = {
    enableVibrateFallback: true,
    ignoreAndroidSystemSettings: false,
  };

/* Screens */
import Splash from './src/views/Splash';
import LocationEnable from './src/views/LocationEnable'; 
import Intro from './src/views/Intro'; 
import Forgot from './src/views/Forgot'; 
import Dashboard from './src/views/Dashboard'; 
import Faq from './src/views/Faq';
import MyRides from './src/views/MyRides'; 
import Wallet from './src/views/Wallet';
import Profile from './src/views/Profile';
import Notifications from './src/views/Notifications';
import TripDetails from './src/views/TripDetails';
import CheckPhone from './src/views/CheckPhone';
import Password from './src/views/Password';
import OTP from './src/views/OTP';
import CreateName from './src/views/CreateName';
import CreateEmail from './src/views/CreateEmail';
import CreatePassword from './src/views/CreatePassword';
import ResetPassword from './src/views/ResetPassword';
import Bill from './src/views/Bill';
import WriteRating from './src/views/WriteRating';
import PrivacyPolicies from './src/views/PrivacyPolicies';
import AboutUs from './src/views/AboutUs';
import Refer from './src/views/Refer';
import ComplaintCategory from './src/views/ComplaintCategory';
import ComplaintSubCategory from './src/views/ComplaintSubCategory';
import FaqDetails from './src/views/FaqDetails'; 
import Promo from './src/views/Promo'; 
import EditFirstName from './src/views/EditFirstName'; 
import EditLastName from './src/views/EditLastName'; 
import EditEmail from './src/views/EditEmail'; 
import Rating from './src/views/Rating';
import NotificationDetails from './src/views/NotificationDetails'; 
import Paypal from './src/views/Paypal';
import CreateComplaint from './src/views/CreateComplaint';
import Chat from './src/views/Chat';
import AppUpdate from './src/views/AppUpdate'; 

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NoInternet from './src/views/NoInternet';



const Stack = createNativeStackNavigator();
const Drawer = createDrawerNavigator();

function CustomDrawerContent(props) {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const [dialog_visible, setDialogVisible] = useState(false);
  const [data, setData] = useState("");
  const [profile_image,setProfileImage]=useState("")
  const [delete_dialog_visible,setDeleteDialogVisible]=useState(false)
  const [imageError, setImageError] = useState(false);

  const menus = [
    {
      icon: 'home',
      name: 'Dashboard',
      route: 'Dashboard',
      color: colors.theme_fg_two,
    },
    {
      icon: 'local-taxi',
      name: 'My Rides',
      route: 'MyRides',
      color: colors.text_grey,
    },
    {
      icon: 'account-circle',
      name: 'Profile Settings',
      route: 'Profile',
      color: colors.text_grey,
    },
    {
      icon: 'payments',
      name: 'Wallet',
      route: 'Wallet',
      color: colors.text_grey,
    },
    {
      icon: 'notifications',
      name: 'Notifications',
      route: 'Notifications',
      color: colors.text_grey,
    },   
    {
      icon: 'share',
      name: 'Refer And Earn',
      route: 'Refer',
      color: colors.text_grey,
    },
    {icon: 'help', name: 'FAQs', route: 'Faq', color: colors.text_grey},
    {
      icon: 'article',
      name: 'Privacy Policies',
      route: 'PrivacyPolicies',
      color: colors.text_grey,
    },
    {
      icon: 'info',
      name: 'About Us',
      route: 'AboutUs',
      color: colors.text_grey,
    },
  ];
  useEffect(()=>{
    get_profile_image();
  })
  const get_profile_image= async ()=>{
    const profile_picture = await AsyncStorage.getItem('profile_picture');
     setProfileImage(profile_picture)
     console.log("profile",profile_picture)
  }

  const showDialog = () => {
    setDialogVisible(true);
  }

  const closeDialog = () => {
    ReactNativeHapticFeedback.trigger("impactLight", options)
    setDialogVisible(false);
  }

  const handleCancel = () => {
    ReactNativeHapticFeedback.trigger("impactLight", options)
    setDialogVisible(false)
  }
  const handleLogout = async () => {
    try {
      ReactNativeHapticFeedback.trigger('impactLight', options);
      closeDialog();
      await AsyncStorage.removeItem('id');

      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: 'CheckPhone'}],
        }),
      );
    } catch (err) {
      console.warn('Logout error:', err);
    }
  };
    
const handleDelete = () => {


  Alert.alert(
    t('Delete Account'),
    t('Are you sure you want to delete your account? This action cannot be undone.'),
    [
      { text: t('Cancel'), style: "cancel" },
      { text: t('Delete'), onPress: () => call_delete_account_api() },
    ]
  );
};

const call_delete_account_api = () => {
  if(global.mode == 'DEMO'){
    Alert.alert(
      t('Demo Mode'), 
      t('Sorry You cannot delete the account in demo mode'),
      [
        {
          text: t('OK'), // Arabic for "OK"
        
        }
      ]
    );
        return;
      }
  console.log({ customer_id: global.id, phone_with_code: global.phone_with_code})
 
  axios({
      method: 'post',
      url: api_url + delete_account_request,
      data: { customer_id: global.id, phone_with_code: global.phone_with_code}
  })
  .then(async response => {
    
      AsyncStorage.clear();
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "CheckPhone" }],
        })
      );
  })
  .catch(error => {
  
      Alert.alert("Sorry Something went wrong")
  });
}
const navigate=(route)=>{
  ReactNativeHapticFeedback.trigger("impactLight", options);
  if (route == 'Delete Account') {
    handleDelete();
  } else {
    navigation.navigate('Home', {screen: route});
  }
}

  return (
    <DrawerContentScrollView
      {...props}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{flexGrow: 1}}>
      {/* Header Section */}
      <View
        style={{
          padding: 20,
          borderBottomWidth: 1,
          borderBottomColor: '#f0f0f0',
          backgroundColor: colors.theme_bg_three,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            }}>
            <DropShadow
              style={{
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.25,
                shadowRadius: 3,
              }}>
              <View
                style={{
                  width: 80,
                  height: 80,
                  borderRadius: 40,
                  backgroundColor: '#f0f0f0',
                  overflow: 'hidden',
                 
                }}>
                {!imageError && profile_image ? (
                  <Image
                    style={{height: '100%', width: '100%'}}
                    source={{uri: img_url + profile_image}}
                    resizeMode="cover"
                    onError={() => setImageError(true)} // If image fails, set error
                  />
                ) : (
                  <Image
                    style={{height: '100%', width: '100%'}}
                    source={profile}
                    resizeMode="cover"
                  />
                )}
              </View>
            </DropShadow>

            <View style={{marginLeft: 15, flex: 1}}>
              <Text
                numberOfLines={1}
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  fontFamily: normal,
                  marginBottom: 2,
                }}>
                {t('Hello')},
              </Text>
              <Text
                numberOfLines={1}
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 20,
                  fontFamily: bold,
                  letterSpacing: 0.5,
                }}>
                {global.first_name}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            onPress={() => navigate('Dashboard')}
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            style={{padding: 5}}>
            <Icon
              type={Icons.MaterialIcons}
              name="close"
              color={colors.theme_fg_two}
              style={{fontSize: 24}}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Menu Items */}
      <View style={{paddingVertical: 10}}>
        {menus.map((item, index) => (
          <TouchableOpacity
            key={item.route}
            activeOpacity={0.7}
            onPress={() => navigate(item.route)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 15,
              paddingHorizontal: 20,
              borderRadius: 10,
              backgroundColor:
                props.state.routeNames[props.state.index] === item.route
                  ? 'rgba(204, 0, 19, 0.1)'
                  : 'transparent',
            }}>
            <View
              style={{
                width: 30,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Icon
                type={Icons.MaterialIcons}
                name={item.icon}
                color={
                  props.state.routeNames[props.state.index] === item.route
                    ? colors.theme_fg_two
                    : item.color
                }
                style={{fontSize: 24}}
              />
            </View>
            <Text
              numberOfLines={1}
              style={{
                color:
                  props.state.routeNames[props.state.index] === item.route
                    ? colors.theme_fg_two
                    : colors.text_grey,
                fontSize: 16,
                fontFamily:
                  props.state.routeNames[props.state.index] === item.route
                    ? normal
                    : regular,
                marginLeft: 15,
                flex: 1,
              }}>
              {t(item.name)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Bottom Actions */}
      <View
        style={{
          marginTop: 'auto',
          paddingVertical: 10,
          borderTopWidth: 1,
          borderTopColor: '#f0f0f0',
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => setDialogVisible(true)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 13,
            paddingHorizontal: 20,
          }}>
          <View
            style={{
              width: 30,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Icon
              type={Icons.MaterialIcons}
              name="logout"
              color={colors.error}
              style={{fontSize: 24}}
            />
          </View>
          <Text
            numberOfLines={1}
            style={{
              color: colors.error,
              fontSize: 16,
              fontFamily: regular,
              marginLeft: 15,
              flex: 1,
            }}>
            {t('Logout')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => setDeleteDialogVisible(true)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 13,
            paddingHorizontal: 20,
          }}>
          <View
            style={{
              width: 30,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Icon
              type={Icons.MaterialIcons}
              name="delete"
              color={colors.error}
              style={{fontSize: 24}}
            />
          </View>
          <Text
            numberOfLines={1}
            style={{
              color: colors.error,
              fontSize: 16,
              fontFamily: regular,
              marginLeft: 15,
              flex: 1,
            }}>
            {t('Delete Account')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Logout Confirmation Modal */}
      <Modal transparent={true} visible={dialog_visible} animationType="fade">
        <View
          style={{
            flex: 1,
            backgroundColor: 'rgba(0,0,0,0.3)',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}>
          <View
            style={{
              backgroundColor: '#FFFFFF',
              borderRadius: 10,
              width: '100%',
              maxWidth: 320,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 4},
              shadowOpacity: 0.1,
              shadowRadius: 20,
              elevation: 10,
              overflow: 'hidden',
            }}>
            {/* Modal Header */}
            <View
              style={{
                padding: 10,
                // borderBottomWidth: 1,
                // borderBottomColor: '#F3F4F6',
              }}>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: regular,
                  color: '#111827',
                  textAlign: 'center',
                  lineHeight: 24,
                }}>
                {t('Confirm Logout')}
              </Text>
            </View>

            {/* Modal Body */}
            <View
              style={{
                padding: 10,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: regular,
                  color: '#6B7280',
                  textAlign: 'center',
                  lineHeight: 22,
                  marginBottom: 8,
                }}>
                {t('Are you sure you want to logout')}?
              </Text>
            </View>

            {/* Modal Footer */}
            <View
              style={{
                flexDirection: 'row',
                borderTopWidth: 1,
                borderTopColor: '#F3F4F6',
              }}>
              <TouchableOpacity
                onPress={closeDialog}
                activeOpacity={0.9}
                style={{
                  flex: 1,
                  paddingVertical: 16,
                  backgroundColor: '#FFFFFF',
                  alignItems: 'center',
                  borderRightWidth: 1,
                  borderRightColor: '#F3F4F6',
                }}>
                <Text
                  style={{
                    color: '#4B5563',
                    fontFamily: regular,
                    fontSize: 15,
                  }}>
                  {t('Cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleLogout}
                activeOpacity={0.9}
                style={{
                  flex: 1,
                  paddingVertical: 16,
                  backgroundColor: '#FFFFFF',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: '#EF4444',
                    fontFamily: regular,
                    fontSize: 15,
                  }}>
                  {t('Logout')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <Modal
        transparent={true}
        visible={delete_dialog_visible}
        animationType="fade">
        <View
          style={{
            flex: 1,
            backgroundColor: 'rgba(0,0,0,0.3)',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}>
          <View
            style={{
              backgroundColor: '#fff',
              borderRadius: 16,
              padding: 0, // Remove padding to allow for full-width borders
              width: '100%',
              maxWidth: 320,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 4},
              shadowOpacity: 0.15,
              shadowRadius: 8,
              elevation: 5,
              overflow: 'hidden', // Ensures borders respect the borderRadius
            }}>
            {/* Modal Header */}
            <View
              style={{
                padding: 24,
                paddingBottom: 16,
              }}>
              <Text
                style={{
                  fontSize: 20,
                  fontFamily: '600',
                  color: '#111827',
                  textAlign: 'center',
                  marginBottom: 4,
                }}>
                {t('Delete Account')}
              </Text>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: '400',
                  color: '#6B7280',
                  textAlign: 'center',
                  lineHeight: 22,
                }}>
                {t(
                  'Are you sure you want to delete your account? This action cannot be undone.',
                )}
              </Text>
            </View>

            {/* Action Buttons */}
            <View
              style={{
                flexDirection: 'row',
                borderTopWidth: 1,
                borderTopColor: '#F3F4F6',
              }}>
              <TouchableOpacity
                onPress={() => setDeleteDialogVisible(false)}
                activeOpacity={0.8}
                style={{
                  flex: 1,
                  padding: 16,
                  alignItems: 'center',
                  borderRightWidth: 1,
                  borderRightColor: '#F3F4F6',
                }}>
                <Text
                  style={{
                    color: '#4B5563',
                    fontFamily: '600',
                    fontSize: 16,
                  }}>
                  {t('Cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={call_delete_account_api}
                activeOpacity={0.8}
                style={{
                  flex: 1,
                  padding: 16,
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: '#EF4444',
                    fontFamily: '600',
                    fontSize: 16,
                  }}>
                  {t('Delete')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </DrawerContentScrollView>
  );
}

function MyDrawer() {
  return (
    <Drawer.Navigator 
      drawerContent={props => <CustomDrawerContent {...props} />} 
      initialRouteName="Dashboard"
      drawerStyle={{ width: 350, backgroundColor:colors.theme_fg_three }}
      screenOptions={{
        drawerStyle: {
          backgroundColor:colors.theme_fg_three,
          width: screenWidth,
        },
      }}
    >
      <Drawer.Screen
        name="Dashboard"
        component={Dashboard}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="MyRides"
        component={MyRides}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="Faq"
        component={Faq}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="Wallet"
        component={Wallet}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="Notifications"
        component={Notifications}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="Profile"
        component={Profile}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="PrivacyPolicies"
        component={PrivacyPolicies}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="AboutUs"
        component={AboutUs}
        options={{headerShown: false}}
      />
      <Drawer.Screen
        name="Refer"
        component={Refer}
        options={{headerShown: false}}
      />
    
     
      
     
    </Drawer.Navigator>
  );
}

function App() {
      const [i18nReady, setI18nReady] = useState(false);
      useEffect(() => {
        i18nInitPromise.then(() => {
          console.log('✅ i18n initialized');
          setI18nReady(true);
        });
      }, []);

      if (!i18nReady) {
        return (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.theme_bg_three,
            }}>
            <View style={{height: '40%', width: '80%'}}>
              <Image
                style={{height: undefined, width: undefined, flex: 1}}
                source={logo}
              />
            </View>
          </View>
        );
      }
   
  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Splash"
          options={{headerShown: false}}>         
        
          <Stack.Screen
            name="Splash"
            component={Splash}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CheckPhone"
            component={CheckPhone}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Password"
            component={Password}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="OTP"
            component={OTP}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreateName"
            component={CreateName}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreateEmail"
            component={CreateEmail}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreatePassword"
            component={CreatePassword}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ResetPassword"
            component={ResetPassword}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="LocationEnable"
            component={LocationEnable}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Intro"
            component={Intro}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Forgot"
            component={Forgot}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Home"
            component={MyDrawer}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="TripDetails"
            component={TripDetails}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Bill"
            component={Bill}
            options={{headerShown: false}}
          />
        
          <Stack.Screen
            name="WriteRating"
            component={WriteRating}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ComplaintCategory"
            component={ComplaintCategory}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ComplaintSubCategory"
            component={ComplaintSubCategory}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="FaqDetails"
            component={FaqDetails}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Promo"
            component={Promo}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="EditFirstName"
            component={EditFirstName}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="EditLastName"
            component={EditLastName}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="EditEmail"
            component={EditEmail}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Rating"
            component={Rating}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="NotificationDetails"
            component={NotificationDetails}
            options={{headerShown: false}}
          />         
          <Stack.Screen
            name="Paypal"
            component={Paypal}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreateComplaint"
            component={CreateComplaint}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Chat"
            component={Chat}
            options={{headerShown: false}}
          />

          <Stack.Screen
            name="AppUpdate"
            component={AppUpdate}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="NoInternet"
            component={NoInternet}
            options={{headerShown: false}}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  btn: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 16,
  },
   overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: 300,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color:colors.theme_bg_two
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color:colors.theme_bg_two
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    flex: 1,
    padding: 10,
    backgroundColor: '#007AFF',
    borderRadius: 5,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderColor:'silver',
    borderWidth:.5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
})

function mapStateToProps(state) {
  const register = state.register || {};
  return {
    first_name: register.first_name || '',
    last_name: register.last_name || '',
    email: register.email || '',
  };
}
const mapDispatchToProps = (dispatch) => ({
  updateEmail: (data) => dispatch(updateEmail(data)),
  updateFirstName: (data) => dispatch(updateFirstName(data)),
  updateLastName: (data) => dispatch(updateLastName(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(App);
