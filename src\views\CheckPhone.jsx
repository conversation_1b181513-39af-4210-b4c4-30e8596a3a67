import {useTranslation} from 'react-i18next';
import '../languages/i18next';

import React, {useState, useRef} from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  StatusBar,
  I18nManager,
  Modal,
  FlatList,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import * as colors from '../assets/css/Colors';
import {
  normal,
  bold,
  regular,
  check_phone,
  api_url,
  btn_loader,
  f_xs,
  f_m,
  f_l,
} from '../config/Constants';
import PhoneInput from 'react-native-phone-input';

import axios from 'axios';
import LottieView from 'lottie-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RNRestart from 'react-native-restart';
import {Picker} from '@react-native-picker/picker';
import Icon, {Icons} from '../components/Icons';
import {SafeAreaView} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
// import ToastManager, { Toast } from "toastify-react-native";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};
const CheckPhone = props => {
  const {t, i18n} = useTranslation();
  const navigation = useNavigation();
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState(false);
  const [formattedValue, setFormattedValue] = useState('');
  const [language, setLanguage] = useState('en');
  const [modalVisible, setModalVisible] = useState(false);
  const languages = [
    {key: 'tr', label: 'Türkçe'},
    {key: 'ar', label: 'العر'},
    {key: 'en', label: 'English'},
  ];

  const phone = useRef(null);

  const go_back = () => {
    navigation.goBack();
  };
  const loadLang = async () => {
    // const savedLang = await AsyncStorage.getItem("language");
    const savedLang = 'en';

    if (savedLang) {
      setLanguage(savedLang);
      i18n.changeLanguage(savedLang);
    }
  };
  const showToast = (type, title, message) => {
    ReactNativeHapticFeedback.trigger('impactHeavy', options);

    Toast.show({
      type: type,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: 'top', // top, bottom
    });
  };
  const check_valid = () => {
    console.log(props.initial_lat);
    if ('+' + phone.current?.getCountryCode() == phone.current?.getValue()) {
      setValidation(false);

      showToast(
        'error',
        t('Validation error'),
        t('Please Enter Valid Phone Number'),
      );
    } else if (!phone.current?.isValidNumber()) {
      setValidation(false);
      showToast(
        'error',
        t('Validation error'),
        t('Please Enter Valid Phone Number'),
      );
    } else {
      setValidation(true);
      //alert(phone.current?.getValue())
      setFormattedValue(phone.current?.getValue());
      call_check_phone(phone.current?.getValue());
    }
  };

  const call_check_phone = async phone_with_code => {
    setLoading(true);
    await axios({
      method: 'post',
      url: api_url + check_phone,
      data: {phone_with_code: phone_with_code},
    })
      .then(async response => {
        setLoading(false);
        navigate(response.data.result);
      })
      .catch(error => {
        setLoading(false);
        console.log(error);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const set_app_language = language => {
    return new Promise(async (resolve, reject) => {
      try {
        AsyncStorage.setItem('lang', language);
        resolve();
      } catch (e) {
        console.error('Failed to set language:', e);
        reject(e);
      }
    });
  };

    async function language_change(lang) {
      setLanguage(lang);
      AsyncStorage.setItem('language', lang);
      await i18n.changeLanguage(lang);
      if (lang === 'ar') {
        if (!I18nManager.isRTL) {
          I18nManager.forceRTL(true);
          I18nManager.allowRTL(true);
          RNRestart.Restart(); // You'll need to restart the app to apply the change
        }
      } else {
        if (I18nManager.isRTL) {
          I18nManager.forceRTL(false);
          I18nManager.allowRTL(false);
          RNRestart.Restart(); // Restart to apply LTR
        }
      }
    }

  const navigate = async data => {
    let phone_number = phone.current?.getValue();
    phone_number = phone_number.replace(
      '+' + phone.current?.getCountryCode(),
      '',
    );
    if (data.is_available == 1) {
      navigation.navigate('Password', {
        phone_number: phone.current?.getValue(),
      });
    } else {
      navigation.navigate('OTP', {
        otp: data.otp,
        phone_with_code: phone.current?.getValue(),
        country_code: '+' + phone.current?.getCountryCode(),
        phone_number: phone_number,
        id: 0,
        from: 'register',
      });
    }
  };

  return (
    <SafeAreaView style={{backgroundColor: colors.lite_bg, flex: 1}}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <View style={[styles.header]} />
      <View style={{margin: 20}} />
      <View style={{alignItems: 'center', justifyContent: 'center'}}>
        <Text
          numberOfLines={1}
          style={{
            color: colors.theme_fg_two,
            fontSize: f_l,
            fontFamily: regular,
          }}>
          {t('Enter your phone number')}
        </Text>
        <View style={{margin: 5}} />
        <Text
          numberOfLines={1}
          style={{color: colors.grey, fontSize: f_xs, fontFamily: normal}}>
          {t('You need enter your phone number')}
        </Text>
        <View style={{margin: 20}} />
        <View style={{width: '80%'}}>
          <PhoneInput
            style={{borderBottomColor: colors.theme_bg_two}}
            flagStyle={styles.flag_style}
            ref={phone}
            initialCountry="tr"
            offset={10}
            textStyle={styles.country_text}
            textProps={{
              placeholder: t('Phone Number'),
              placeholderTextColor: colors.theme_fg_two,
            }}
            autoFormat={true}
          />
          <View style={{margin: 30}} />

          {loading == false ? (
            <View>
              <TouchableOpacity
                onPress={check_valid.bind(this)}
                activeOpacity={1}
                style={{
                  width: '100%',
                  backgroundColor: colors.btn_color,
                  borderRadius: 10,
                  height: 50,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_m,
                    color: colors.theme_fg_three,
                    fontFamily: normal,
                  }}>
                  {t('Login')} / {t('Register')}
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={{height: 50, width: '90%', alignSelf: 'center'}}>
              <LottieView style={{flex: 1}} source={btn_loader} autoPlay loop />
            </View>
          )}
          <View style={{margin: 20}} />
          <TouchableOpacity
            onPress={() => setModalVisible(true)}
            style={{
              alignSelf: 'center',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Icon
              type={Icons.Ionicons}
              name="language-outline"
              color={colors.theme_fg_two}
              style={{fontSize: 30}}
            />
            {i18n.language == 'tr' ? (
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: f_m,
                  fontFamily: regular,
                }}>
                {t('Türkçe')}
              </Text>
            ) : (
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: f_m,
                  fontFamily: regular,
                }}>
                {t('Türkçe')}
              </Text>
            )}
          </TouchableOpacity>

          <Modal
            transparent={true}
            animationType="slide"
            visible={modalVisible}
            onRequestClose={() => setModalVisible(false)}>
            <View
              style={{
                flex: 1,
                backgroundColor: 'rgba(0,0,0,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                padding: 20,
              }}>
              <View
                style={{
                  backgroundColor: '#ffffff',
                  borderRadius: 20,
                  padding: 25,
                  width: '90%',
                  maxWidth: 350,
                  shadowColor: '#000',
                  shadowOffset: {width: 0, height: 5},
                  shadowOpacity: 0.3,
                  shadowRadius: 15,
                  elevation: 10,
                }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: 'bold',
                    color: '#2c3e50',
                    marginBottom: 20,
                    textAlign: 'center',
                    borderBottomWidth: 2,
                    borderBottomColor: '#3498db',
                    paddingBottom: 10,
                  }}>
                  {t('Change Language')}
                </Text>

                <FlatList
                  data={languages}
                  renderItem={({item}) => (
                    <TouchableOpacity
                      onPress={() => language_change(item.key)}
                      style={{
                        padding: 15,
                        marginVertical: 5,
                        backgroundColor:
                          i18n.language === item.key ? '#f7f7f7' : '#f8f9fa',
                        borderRadius: 10,
                        borderLeftWidth: 4,
                        borderLeftColor:
                          i18n.language === item.key
                            ? colors.theme_bg
                            : 'transparent',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <Text
                        style={{
                          fontSize: 18,
                          color:
                            i18n.language === item.key ? '#2c3e50' : '#7f8c8d',
                        }}>
                        {item.label}
                      </Text>
                      {i18n.language === item.key && (
                        <View
                          style={{
                            width: 20,
                            height: 20,
                            borderRadius: 10,
                            backgroundColor: colors.theme_bg,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <Text style={{color: 'white', fontSize: 14}}>✓</Text>
                        </View>
                      )}
                    </TouchableOpacity>
                  )}
                  keyExtractor={item => item.key}
                />

                <TouchableOpacity
                  onPress={() => setModalVisible(false)}
                  style={{
                    marginTop: 20,
                    padding: 15,
                    backgroundColor: colors.theme_bg,
                    borderRadius: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#3498db',
                    shadowOffset: {width: 0, height: 3},
                    shadowOpacity: 0.3,
                    shadowRadius: 5,
                  }}>
                  <Text
                    style={{
                      color: 'white',
                      fontSize: 18,
                      fontWeight: '600',
                    }}>
                    {t('Close')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
      </View>
      
      <Toast />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 60,
    backgroundColor: colors.lite_bg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textinput: {
    fontSize: f_l,
    color: colors.grey,
    fontFamily: regular,
    height: 60,
    backgroundColor: '#FAF9F6',
  },
  flag_style: {
    width: 38,
    height: 24,
  },
  country_text: {
    fontSize: 18,
    borderBottomWidth: 1,
    paddingBottom: 8,
    height: 35,
    fontFamily: regular,
    color: colors.theme_fg_two,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    color: 'black',
    fontFamily: bold,
    marginBottom: 20,
  },
  languageOption: {
    padding: 10,
    width: '100%',
  },
  languageText: {
    color: 'black',
    fontSize: 16,
    fontFamily: regular,
  },
  closeButton: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#2196F3',
    borderRadius: 5,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: regular,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});

export default CheckPhone;
