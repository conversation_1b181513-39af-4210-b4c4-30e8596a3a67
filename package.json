{"name": "cab2door_customer", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@googlemaps/polyline-codec": "^1.0.28", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/template": "^0.77.1", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/database": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-native-picker/picker": "^2.11.0", "@react-native-segmented-control/segmented-control": "^2.5.7", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "axios": "^1.8.4", "flutterwave-react-native": "^1.0.4", "geofire-common": "^6.0.0", "i18next": "^24.2.3", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "react": "18.3.1", "react-i18next": "^15.4.1", "react-native": "0.77.1", "react-native-android-location-enabler": "^2.0.1", "react-native-drop-shadow": "^1.0.3", "react-native-eject": "^1.0.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.8.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^8.2.0", "react-native-keyboard-controller": "^1.16.7", "react-native-maps": "^1.20.1", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.13.1", "react-native-permissions": "^5.3.0", "react-native-phone-input": "^1.3.7", "react-native-picker-select": "^9.3.1", "react-native-ratings": "^8.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.17.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.9.1", "react-native-sound": "^0.11.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-version-number": "^0.3.6", "react-native-webview": "^13.13.2", "react-redux": "^9.2.0", "redux": "^5.0.1", "toastify-react-native": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}