import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useEffect, useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  StatusBar,
  Dimensions,
  TouchableOpacity,
  Animated,
} from "react-native";
import * as colors from "../assets/css/Colors";
import Icon, { Icons } from "../components/Icons";
import {
  normal,
  bold,
  screenWidth,
  f_s,
  f_25,
  regular,
  screenHeight,
  f_30,
  f_m,
} from "../config/Constants";
import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { SafeAreaView } from "react-native-safe-area-context";
import PagerView from "react-native-pager-view";
const { width, height } = Dimensions.get("window");

const Intro = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const pagerRef = useRef(null);
  const [pageIndex, setPageIndex] = useState(0);
  const slideAnim = useRef(new Animated.Value(50)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    startAnimation();
  }, [pageIndex]);

  const startAnimation = () => {
    fadeAnim.setValue(0);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  };
  useEffect(() => {
    slideAnim.setValue(50);
    fadeAnim.setValue(0);

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, [pageIndex]);

  const handleNext = () => {
    if (pageIndex < slides.length - 1) {
      pagerRef.current.setPage(pageIndex + 1);
    } else {
      onDone();

      console.log("Onboarding Complete");
    }
  };
  const handleSkip = () => {
    onDone();
    // pagerRef.current.setPage(slides.length - 1);
  };

  const slides = [
    {
      key: 1,
      title: "Book a Ride",
      mainTitle: "Find the Best Taxi",
      text: "Choose your preferred vehicle model and fare based on your needs and the number of passengers.",
      image: require("../assets/json/slider_1.json"),
      bg: "#FFFFFF",
    },
    {
      key: 2,
      title: "Set Your Route",
      mainTitle: "Plan Your Trip",
      text: "Log in to the app, enter your pickup and drop-off locations, and get fare estimates instantly.",
      image: require("../assets/json/slider_2.json"),
      bg: "#FFFFFF",
    },
    {
      key: 3,
      title: "Enjoy Your Ride",
      mainTitle: "Reach Your Destination",
      text: "Sit back, relax, and arrive at your destination safely and on time with our reliable drivers.",
      image: require("../assets/json/slider_3.json"),
      bg: "#FFFFFF",
    },
  ];

  const onDone = async () => {
    try {
      await AsyncStorage.setItem("existing", "1");
      global.existing = await 1;
      navigation.navigate("CheckPhone");
    } catch (e) {
      alert(e);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: slides[pageIndex].bg }}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <Animated.Text
        style={{
          fontSize: 30,
          fontWeight: "bold",
          color: "#101010", // Primary Blue
          textAlign: "center",
          marginTop: 50,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }}
      >
        {slides[pageIndex].mainTitle}
      </Animated.Text>

      <PagerView
        style={{ flex: 1 }}
        initialPage={0}
        ref={pagerRef}
        onPageSelected={(e) => setPageIndex(e.nativeEvent.position)}
      >
        {slides.map((item) => (
          <View
            key={item.key}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              paddingHorizontal: 20,
            }}
          >
            <LottieView
              style={{ width: width * 0.7, height: height * 0.4 }}
              source={item.image}
              autoPlay
              loop
            />

            <Text
              style={{
                fontSize: 24,
                fontWeight: "bold",
                marginTop: 20,
                textAlign: "center",
                color: "grey",
              }}
            >
              {item.title}
            </Text>

            {/* Description */}
            <Text
              style={{
                fontSize: 16,
                textAlign: "center",
                marginTop: 10,
                color: "#888888",
                textAlign: "justify",
              }}
            >
              {item.text}
            </Text>
          </View>
        ))}
      </PagerView>

      {pageIndex < slides.length - 1 && (
        <TouchableOpacity
          onPress={handleSkip}
          style={{
            position: "absolute",
            top: 40,
            right: 20,
            padding: 10,
            borderColor: "silver",
            borderWidth: 0.2,
            borderRadius: 10,
          }}
        >
          <Text
            style={{
              fontSize: f_m,
              color: colors.text_grey,
              fontWeight: "bold",
            }}
          >
            Skip
          </Text>
        </TouchableOpacity>
      )}

      <View
        style={{
          flexDirection: "row",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingHorizontal: 20,
          paddingBottom: 20,
        }}
      >
        <TouchableOpacity
          onPress={handleNext}
          style={{
            backgroundColor: colors.theme_bg,
            padding: 12,
            borderRadius: 50,
            elevation: 5,
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {pageIndex === slides.length - 1 ? (
            <Icon
              type={Icons.Ionicons}
              name="checkmark"
              color={colors.theme_bg_three}
              style={{ fontSize: f_30 }}
            />
          ) : (
            <Icon
              type={Icons.MaterialIcons}
              name="arrow-forward"
              color={colors.theme_bg_three}
              style={{ fontSize: f_30 }}
            />
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  textFieldcontainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginTop: 5,
    marginBottom: 5,
    height: 45,
  },
  textFieldIcon: {
    padding: 5,
  },
  textField: {
    flex: 1,
    padding: 12,
    borderRadius: 10,
    height: 45,
    backgroundColor: colors.theme_bg_three,
    fontSize: 14,
    color: colors.grey,
  },
  button: {
    padding: 10,
    borderRadius: 10,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.theme_bg,
    width: "100%",
    height: 45,
  },
  flag_style: {
    width: 38,
    height: 24,
  },
  country_text: {
    flex: 1,
    padding: 12,
    borderRadius: 10,
    height: 45,
    backgroundColor: colors.theme_bg_three,
    fontSize: f_s,
    color: colors.theme_fg_two,
  },
  textFieldcontainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginTop: 5,
    marginBottom: 5,
    height: 45,
  },
  slide: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingBottom: 96,
  },
  image: {
    width: 320,
    height: 320,
    marginTop: 32,
  },
  title: {
    fontSize: f_25,
    color: colors.theme_fg_two,
    fontFamily: regular,
    textAlign: "center",
  },
  text: {
    fontSize: f_s,
    fontFamily: normal,
    color: colors.text_grey,
    marginTop: 20,
    alignItems: "center",
    justifyContent: "center",
    textAlign: "justify",
    padding: 20,
  },
  buttonCircle: {
    width: 40,
    height: 40,
    backgroundColor: colors.theme_bg,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default Intro;
