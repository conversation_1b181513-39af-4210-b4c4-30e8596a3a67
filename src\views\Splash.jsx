import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Platform,
  PermissionsAndroid,
  StatusBar,
  Alert,
  Text,Animated,
  Linking
} from "react-native";
import { useNavigation, CommonActions, useFocusEffect } from "@react-navigation/native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import Geolocation from '@react-native-community/geolocation';
import VersionNumber from 'react-native-version-number';
import { isLocationEnabled, promptForEnableLocationIfNeeded } from 'react-native-android-location-enabler';
import axios from 'axios';
import NetInfo from "@react-native-community/netinfo";
import {getApp} from '@react-native-firebase/app';
import {
  getMessaging,
  getToken,
  requestPermission,
  onMessage,
  AuthorizationStatus,
} from '@react-native-firebase/messaging';
import * as colors from "../assets/css/Colors";
import { bold, logo, app_name, LATITUDE_DELTA, LONGITUDE_DELTA, app_settings, api_url, f_s, f_m } from "../config/Constants";
import { connect } from 'react-redux';
import { initialLat, initialLng, initialRegion } from '../actions/BookingActions';
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
import notifee, {AndroidImportance} from '@notifee/react-native';
import moment from 'moment';
const Splash = (props) => {
  const navigation = useNavigation();
  const isNavigating = useRef(false);
  const {t, i18n} = useTranslation();  

  useFocusEffect(
    useCallback(() => {
        if (i18n.language === 'ar') {
          moment.locale('ar');
        } else {
          moment.locale('en');
        }


        checkToken();
      

      return () => {
      };
    }, []),
  );


const checkToken = async () => {
  console.log('checkToken called');
  const app = getApp(); // Get Firebase app instance
  const messagingInstance = getMessaging(app);

  try {
    // Request permissions first
    await requestNotificationPermission();
    await setupNotificationChannel();

    // Subscribe to foreground notifications
    const unsubscribe = onMessage(messagingInstance, async remoteMessage => {
      console.log('Received foreground notification:', remoteMessage);
    });

    console.log('Requesting notification permission...');
    const authStatus = await requestPermission(messagingInstance);
    const enabled =
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      Alert.alert(t('Sorry Permission Denied'));
      return unsubscribe; // Return the unsubscribe function
    }

    // Get FCM token
    const fcmToken = await getToken(messagingInstance);

    if (fcmToken) {
      console.log('FCM Token:', fcmToken);
      global.fcm_token = fcmToken;
      checkData();
      return unsubscribe; // Return the unsubscribe function
    } else {
      Alert.alert(t('Sorry unable to get your token'));
      return unsubscribe; // Return the unsubscribe function
    }
  } catch (error) {
    console.error('FCM token retrieval error:', error);
    Alert.alert(t('Sorry unable to get your token'));
    // If unsubscribe was created before the error, return it
    if (typeof unsubscribe !== 'undefined') {
      return unsubscribe;
    }
  }
};

async function setupNotificationChannel() {
  console.log('Setting up notification channel...');
  await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
    importance: AndroidImportance.HIGH,
  });
}

async function requestNotificationPermission() {
  console.log('Requesting notification permission...');
  const settings = await notifee.requestPermission();
  console.log('Notification Permission Settings:', settings);
}



  const checkData = async () => {
    if (!isNavigating.current) {
      isNavigating.current = true;
      await callSettingsWithRetry();
    }
    else{
      callSettingsWithRetry()
    }
  }
  const requestLocationPermission = async () => {
    try {
      // Check if the location permission is already granted
      const isGranted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      
      if (!isGranted) {
        // If not granted, prompt the user for permission
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'App Access your location for tracking in background',
            message: `${app_name} will track your location in background when the app is closed or not in use.`,
            buttonPositive: 'OK',
          }
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          await getInitialLocation();
        } else {
          console.log('Permission denied');
          // Alert the user that permission is required
          Alert.alert(
            'Location Permission Required',
            'The app needs location access to function correctly. You can enable it from the app settings.',
            [
              {
                text: 'Cancel',
                onPress: () => console.log('Permission denied, user canceled'),
                style: 'cancel',
              },
              {
                text: 'Open Settings',
                onPress: () => Linking.openSettings(),
              },
            ]
          );
        }
      } else {
        // If permission is already granted
        await getInitialLocation();
      }
  
 
    } catch (err) {
      console.warn(err);
      navigation.navigate('LocationEnable');
    }
  };
  


  const getInitialLocation = async () => {
    Geolocation.getCurrentPosition(
      async (position) => {
        let location = position.coords;
        let region = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA
        }
        await props.initialRegion(region);
        await props.initialLat(location.latitude);
        await props.initialLng(location.longitude);
        
        navigate();
      },
      error => {
        navigation.navigate('LocationEnable');
      },
      { enableHighAccuracy: false, timeout: 10000 }
    );
  }

  const callSettingsWithRetry = async (retryCount = 0) => {
    try {
      const response = await axios.get(api_url + app_settings);
      await home(response.data.result);
    } catch (error) {
      if (error.response && error.response.status === 429 && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
        setTimeout(() => {
          callSettingsWithRetry(retryCount + 1);
        }, delay);
      } else {
        console.log(error);
        Alert.alert(t('Sorry something went wrong'));
      }
    }
  }

  const home = async (data) => {
    const id = await AsyncStorage.getItem('id');
    const first_name = await AsyncStorage.getItem('first_name');
    const profile_picture = await AsyncStorage.getItem('profile_picture');
    const phone_with_code = await AsyncStorage.getItem('phone_with_code');
    const email = await AsyncStorage.getItem('email');

    global.existing = await AsyncStorage.getItem("existing");
    global.stripe_key = data.stripe_key;
    global.razorpay_key = data.razorpay_key;
    global.paystack_public_key = data.paystack_public_key;
    global.paystack_secret_key = data.paystack_secret_key;
    global.flutterwave_public_key = data.flutterwave_public_key;
    global.app_name = data.app_name;
    global.language_status = data.language_status;
    global.default_language = data.default_language;
    global.polyline_status = data.polyline_status;
    global.currency = data.default_currency_symbol;
    global.currency_short_code = data.currency_short_code;


    global.mode = data.mode;
    global.promo_id = 0;

    if (id !== null) {
      global.id = id;
      global.first_name = first_name;
      global.profile_picture = profile_picture;
      global.phone_with_code = phone_with_code;
      global.email = email;
    } else {
      global.id = 0;
    }
    checkLocation();
  }
 useEffect(() => {
   const checkConnection = () => {
     NetInfo.fetch().then((state) => {
       if (!state.isConnected) {
         ReactNativeHapticFeedback.trigger("impactLight", options);
         navigation.navigate("NoInternet");
       }
     });
   };
   checkConnection();
   const interval = setInterval(checkConnection, 10000);
   return () => clearInterval(interval);
 }, []);
  const checkLocation = async () => {
    if (Platform.OS === "android") {
      promptForEnableLocationIfNeeded({ interval: 10000, fastInterval: 5000 })
        .then(async data => {
          await requestLocationPermission();
        }).catch(err => {
          console.log(err);
          navigation.navigate('LocationEnable');
        });
    } else {
      await getInitialLocation();
    }
  }

  const navigate = () => {
    console.log('Navigating to the next screen...');
    if (global.existing == 1) {
      if (global.id > 0) {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "Home" }],
          })
        );
      } else {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "CheckPhone" }],
          })
        );
      }
    } else {
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "Intro" }],
        })
      );
    }
  }

  return (
    <View style={styles.background}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <View style={styles.logo_container}>
        <Image style={styles.logo} source={logo} />
      </View>

    </View>
  );
}

const styles = StyleSheet.create({
  background: {
    height:'100%',
    width:'100%',
    alignItems: 'center',
    justifyContent:'center',
    backgroundColor:colors.theme_fg_three,
  },
  logo_container:{
    height:'50%', 
    width:'80%'
  },
  logo:{ 
    height: undefined,
    width: undefined,
    flex: 1
  },
  typingText: {
    fontSize: 14,
    fontFamily: 'regular',
    color: '#000', // Replace with colors.theme_fg_two
    letterSpacing: 1,
},
boldText: {
    fontSize: 16,
    fontFamily: bold,
    color: '#000', // Replace with colors.theme_fg_two
    letterSpacing: 1,
},

});

function mapStateToProps(state) {
  return {
    state: state,
  };
}

const mapDispatchToProps = (dispatch) => ({
  initialLat: (data) => dispatch(initialLat(data)),
  initialLng: (data) => dispatch(initialLng(data)),
  initialRegion: (data) => dispatch(initialRegion(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(Splash);