import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useEffect, useRef } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,
    Image,
    TextInput,
    StatusBar
} from "react-native";
import { useNavigation, CommonActions, useRoute } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import { api_url, reset_password, normal, bold, regular, success_icon, btn_loader, f_xl, f_xs, f_m } from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import axios from 'axios';
import LottieView from 'lottie-react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const ResetPassword = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const route = useRoute();
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [id, setId] = useState(route.params.id);
    const [changed_status, setChangedStatus] = useState(0);
    const [confirm_password, setConfirmPassword] = useState('');
    const [secureTextEntry, setSecureTextEntry] = useState(true);
    const inputRef = useRef();

    const go_back = () => {
        navigation.goBack();
    }

    useEffect(() => {
        setTimeout(() => inputRef.current.focus(), 100)
    }, []);


    const check_valid = () => {
        if (password) {
            check_password();
        } else {
           showToast(
             "error",
             t('Validation error'),
             t('Please enter your password')
           );
          /*   dropDownAlertRef({
                type: DropdownAlertType.Error,
                title: t('Validation error'),
                message: t('Please enter your password'),
              }); */
        }
    }

    const check_password = () => {
        if (password == confirm_password) {
            call_reset_password();
        } else {
           showToast(
             "error",
             t('Validation error'),
             t('Your password and confirm password did not match')
           );
          
        }
    }
 const showToast = (type, title, message) => {
ReactNativeHapticFeedback.trigger("impactHeavy", options);

    Toast.show({
      type: type ,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: "top", // top, bottom
    });
  };
    const call_reset_password = async () => {
        setLoading(true);
        await axios({
            method: 'post',
            url: api_url + reset_password,
            data: { id: id, password: password }
        })
            .then(async response => {
                setLoading(false);
                setChangedStatus(1);
            })
            .catch(error => {
                setLoading(false);
                 showToast(
                   "error",
                   t('Error'),
                   t('Sorry something went wrong')
                 );
             
            });
    }

    const navigate = () => {
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: "CheckPhone" }],
            })
        );
    }


    return (
      <SafeAreaView style={{ backgroundColor: colors.lite_bg, flex: 1 }}>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle="dark-content"
        />
        <View style={[styles.header]}>
          {changed_status == 0 && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={go_back.bind(this)}
              style={{
                width: "15%",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Icon
                type={Icons.MaterialIcons}
                name="arrow-back"
                color={colors.theme_fg_two}
                style={{ fontSize: 30 }}
              />
            </TouchableOpacity>
          )}
        </View>
        <View style={{ margin: 20 }} />
        {changed_status == 0 ? (
          <View style={{ alignItems: "center", justifyContent: "center" }}>
            <Text
              numberOfLines={1}
              style={{
                color: colors.theme_fg_two,
                fontSize: f_xl,
                fontFamily: regular,
              }}
            >
              {t('Reset your password')}
            </Text>
            <View style={{ margin: 5 }} />
            <Text
              numberOfLines={1}
              style={{ color: colors.grey, fontSize: f_xs, fontFamily: normal }}
            >
              {t('Create your new password')}
            </Text>
            <View style={{ margin: 20 }} />
            <View style={{ width: "80%" }}>
              <View style={{ flexDirection: "row" }}>
                <View
                  style={{
                    width: "20%",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: colors.theme_bg_three,
                  }}
                >
                  <Icon
                    type={Icons.MaterialIcons}
                    name="lock"
                    color={colors.theme_fg_two}
                    style={{ fontSize: 30 }}
                  />
                </View>
                <View
                  style={{
                    width: "65%",
                    alignItems: "flex-start",
                    paddingLeft: 10,
                    justifyContent: "center",
                    backgroundColor: colors.text_container_bg,
                  }}
                >
                  <TextInput
                    ref={inputRef}
                    placeholder={t('Password')}
                    secureTextEntry={secureTextEntry}
                    placeholderTextColor={colors.grey}
                    style={styles.textinput}
                    onChangeText={(TextInputValue) =>
                      setPassword(TextInputValue)
                    }
                  />
                </View>
                <TouchableOpacity
                  onPress={() => setSecureTextEntry(!secureTextEntry)}
                  style={{
                    backgroundColor: colors.text_container_bg,
                    width: "15%",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Icon
                    type={Icons.MaterialIcons}
                    name={secureTextEntry ? "visibility-off" : "visibility"}
                    color={colors.grey}
                    style={{ fontSize: 25, marginRight: 10 }}
                  />
                </TouchableOpacity>
              </View>
              <View style={{ margin: 10 }} />
              <View style={{ flexDirection: "row" }}>
                <View
                  style={{
                    width: "20%",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: colors.theme_bg_three,
                  }}
                >
                  <Icon
                    type={Icons.MaterialIcons}
                    name="lock"
                    color={colors.theme_fg_two}
                    style={{ fontSize: 30 }}
                  />
                </View>
                <View
                  style={{
                    width: "65%",
                    alignItems: "flex-start",
                    paddingLeft: 10,
                    justifyContent: "center",
                    backgroundColor: colors.text_container_bg,
                  }}
                >
                  <TextInput
                    ref={inputRef}
                    placeholder={t('Confirm Password')}
                    secureTextEntry={secureTextEntry}
                    placeholderTextColor={colors.grey}
                    style={styles.textinput}
                    onChangeText={(TextInputValue) =>
                      setConfirmPassword(TextInputValue)
                    }
                  />
                </View>
                <TouchableOpacity
                  onPress={() => setSecureTextEntry(!secureTextEntry)}
                  style={{
                    backgroundColor: colors.text_container_bg,
                    width: "15%",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Icon
                    type={Icons.MaterialIcons}
                    name={secureTextEntry ? "visibility-off" : "visibility"}
                    color={colors.grey}
                    style={{ fontSize: 25, marginRight: 10 }}
                  />
                </TouchableOpacity>
              </View>
              <View style={{ margin: 30 }} />

              {loading == false ? (
                <TouchableOpacity
                  onPress={check_valid.bind(this)}
                  activeOpacity={1}
                  style={{
                    width: "100%",
                    backgroundColor: colors.btn_color,
                    borderRadius: 10,
                    height: 50,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      color: colors.theme_fg_two,
                      fontSize: f_m,
                      color: colors.theme_fg_three,
                      fontFamily: normal,
                    }}
                  >
                    {t('Change Password')}
                  </Text>
                </TouchableOpacity>
              ) : (
                <View style={{ height: 50, width: "90%", alignSelf: "center" }}>
                  <LottieView
                    style={{ flex: 1 }}
                    source={btn_loader}
                    autoPlay
                    loop
                  />
                </View>
              )}
            </View>
          </View>
        ) : (
          <View style={{ alignItems: "center", justifyContent: "center" }}>
            <Text
              numberOfLines={1}
              style={{
                color: colors.success,
                fontSize: f_xl,
                fontFamily: bold,
              }}
            >
              Done!
            </Text>
            <View style={{ margin: 5 }} />
            <Text
              style={{
                color: colors.grey,
                fontSize: f_xs,
                fontFamily: normal,
                textAlign: "center",
                width: "80%",
              }}
            >
              {t('Password has been changed successfully')}
            </Text>
            <View style={{ margin: 20 }} />
            <View style={{ height: 150, width: 150 }}>
              <Image
                source={success_icon}
                style={{ height: undefined, width: undefined, flex: 1 }}
              />
            </View>
            <View style={{ margin: 20 }} />
            <View style={{ width: "80%" }}>
              <TouchableOpacity
                onPress={navigate.bind(this)}
                activeOpacity={1}
                style={{
                  width: "100%",
                  backgroundColor: colors.btn_color,
                  borderRadius: 10,
                  height: 50,
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_m,
                    color: colors.theme_fg_three,
                    fontFamily: normal,
                  }}
                >
                  {t('Login')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        <Toast />        
      </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    header: {
        height: 60,
        backgroundColor: colors.lite_bg,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textinput: {
        fontSize: f_m,
        color: colors.grey,
        fontFamily: regular,
        height: 60,
        backgroundColor: colors.text_container_bg,
        width: '100%'
    },
});

export default ResetPassword;