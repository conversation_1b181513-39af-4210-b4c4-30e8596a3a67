import { Toast } from 'toastify-react-native';

const ToastComponent = (title, message, type = 'error', options = {}) => {
  if (!message) return;

  const fullMessage = title ? `${title}: ${message}` : message;

  Toast[type](fullMessage, 'bottom', {
    style: {
      fontSize: options.fontSize || 16, // Customize font size
      fontWeight: options.fontWeight || 'bold',
      backgroundColor: options.backgroundColor || (type === 'success' ? 'green' : 'red'),
      color: options.textColor || 'white',
      padding: options.padding || 10,
      borderRadius: options.borderRadius || 8,
    },
  });
};

export default ToastComponent;
