import { useTranslation } from "react-i18next";
import '../languages/i18next';

//Fixed
import React, { useState, useEffect, useCallback } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,
    ScrollView,
    Image,
    StatusBar,
    TextInput,
    ActivityIndicator
} from "react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import Icon, { Icons } from '../components/Icons';
import { bold, regular, api_url, get_profile, profile_picture_upload, profile_picture_update, img_url, f_xl, f_xs, f_m, f_l, profile, normal } from '../config/Constants';
import axios from 'axios';
import * as ImagePicker from "react-native-image-picker";
import { updateFirstName } from '../actions/RegisterActions';
import { connect } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import RNFS from "react-native-fs";

import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const haptic_option = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const options = {
    title: 'Select a photo',
    takePhotoButtonTitle: 'Take a photo',
    chooseFromLibraryButtonTitle: 'Choose from gallery',
    base64: true,
    quality: 1,
    maxWidth: 500,
    maxHeight: 500,
};

const Profile = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const [loading, setLoading] = useState(false);
    const [on_load, setOnLoad] = useState(0);
    const [data, setData] = useState("");
    const go_back = () => {
      ReactNativeHapticFeedback.trigger("impactLight", haptic_option);
      navigation.toggleDrawer();
    }

    useFocusEffect(
        useCallback(() => {
          call_get_profile();  // This will run when the screen is focused  
          // Optionally, you can return a cleanup function here if needed
          return () => {
            // Cleanup if necessary when the screen loses focus
          };
        }, [])
      );
    const call_get_profile = () => {

        setLoading(true);
        axios({
            method: 'post',
            url: api_url + get_profile,
            data: { customer_id: global.id, lang: i18n.language }
        })
        .then(async response => {
            setLoading(false);
            setData(response.data.result)
            setOnLoad(1);
            props.updateFirstName(response.data.result.first_name);
        })
        .catch(error => {
            setLoading(false);
               showToast(
                 "error",
                 t('Error'),
                 t('Sorry something went wrong')
               );
        });
    }

    const navigate = (route) => {
        navigation.navigate(route);
    }
  const showToast = (type, title, message) => {
ReactNativeHapticFeedback.trigger("impactLight", haptic_option);

    Toast.show({
      type: type ,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: "top", // top, bottom
    });
  };
  const select_photo = async () => {
    const options = {
      mediaType: "photo",
      quality: 1,
    };

    ImagePicker.launchImageLibrary(options, async (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorMessage) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const imageUri = response.assets[0].uri;

        try {
          const base64String = await RNFS.readFile(imageUri, "base64");

          call_profile_picture_upload(base64String);
        } catch (err) {
          console.log("Error converting image to Base64:", err);
        }
      }
    });
  };

const call_profile_picture_upload = async (data_img) => {
  setLoading(true);

  try {
    const url = api_url + profile_picture_upload;

    // Create form data
    const formData = new FormData();
    formData.append("image", {
      name: "image.png",
      type: "image/png",
      uri: `data:image/png;base64,${data_img}`, // Base64 Data URI format
    });

    // Make API request with Axios
    const response = await axios.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    if (response.data.result) {
      call_profile_picture_update(response.data.result);
    }
  } catch (err) {
    console.log("Error uploading image:", err);
    setLoading(false);
    showToast(
      "error",
      t('Error'),
      t('Error on while upload try again later')
    );
  }
};

    const call_profile_picture_update = async (data) => {
     
        await axios({
            method: 'post',
            url: api_url + profile_picture_update,
            data: { id: global.id, profile_picture: data }
        })
        .then(async response => {
            if (response.data.status == 1) {
            setLoading(false);

                showToast(
                  "success",
                  t('Success'),
                  t('Your Profile Picture Update Successfully')
                );
                saveProfilePicture(data);
                call_get_profile()

            } else {
               showToast("error", t('Error'), response.data.message);
            }
        })
        .catch(error => {
            setLoading(false);
            showToast(
              "error",
              t('Error'),
              t('Sorry something went wrong')
            );
        });
    }

    const saveProfilePicture = async(data) =>{
        try{
            await AsyncStorage.setItem('profile_picture', data.toString());
            call_get_profile();
            global.profile_picture = await data.profile_picture;
          }catch (e) {
             showToast("error", t('Error'), e);
            alert(e);
        }
      }

    return (
      <SafeAreaView style={{ backgroundColor: colors.lite_bg, flex: 1 }}>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle="dark-content"
        />
        <View style={[styles.header]}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={go_back.bind(this)}
            style={{
              width: "15%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon
              type={Icons.MaterialIcons}
              name="arrow-back"
              color={colors.theme_fg_two}
              style={{ fontSize: 30 }}
            />
          </TouchableOpacity>
          <View
            activeOpacity={1}
            style={{
              width: "85%",
              alignItems: "flex-start",
              justifyContent: "center",
            }}
          >
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{
                width: undefined,
                height: undefined,
                color: colors.theme_fg_two,
                fontSize: f_xl,
                fontFamily: regular,
              }}
            >
              {t('Edit Profile')}
            </Text>
          </View>
        </View>
        <ScrollView>
          {on_load == 1 && (
            <View>
              <TouchableOpacity
                activeOpacity={1}
                onPress={select_photo.bind(this)}
                style={{
                  alignItems: "center",
                  justifyContent: "center",
                  borderWidth: 3,
                  borderRadius: 65,
                  padding: 2,
                  width: 130,
                  height: 130,
                  borderColor: colors.grey,
                  borderStyle: "dotted",
                  alignSelf: "center",
                  margin: 20,
                }}
              >
                <View style={{ width: 120, height: 120 }}>
                  {loading ? (
                    <View style={{width:'100%',height:'100%',justifyContent:'center',alignItems:'center'}}>

                    <ActivityIndicator size={50} color={colors.theme_bg} style={{alignSelf:'center',justifyContent:'center'}}/>
                    </View>
                  ) : (
                    <Image
                      style={{
                        height: undefined,
                        width: undefined,
                        flex: 1,
                        borderRadius: 75,
                      }}
                      source={{ uri: img_url + data.profile_picture }}
                    />
                  )}
                  
                </View>
              </TouchableOpacity>
              <ScrollView>
                <View style={{ alignItems: "center" }}>
                  <Text
                    style={{
                      color: colors.theme_fg_two,
                      fontSize: f_l,
                      fontFamily: regular,
                    }}
                  >
                    {t('Say a little bit about yourself')}...
                  </Text>
                  <View style={{ margin: 20 }} />
                  <View style={{ width: "90%" }}>
                    <View style={{ marginBottom: 20 }}>
                      <Text
                        style={{
                          color: colors.text_grey,
                          fontSize: f_xs,
                          fontFamily: normal,
                        }}
                      >
                        {t('First Name')}
                      </Text>
                      <View style={{ margin: 5 }} />
                      <TouchableOpacity
                        activeOpacity={1}
                        onPress={navigate.bind(this, "EditFirstName")}
                        style={{ flexDirection: "row" }}
                      >
                        <View
                          style={{
                            width: "15%",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: colors.theme_bg_three,
                          }}
                        >
                          <Icon
                            type={Icons.MaterialIcons}
                            name="person"
                            color={colors.theme_fg_two}
                            style={{ fontSize: 30 }}
                          />
                        </View>
                        <View
                          style={{
                            width: "85%",
                            alignItems: "flex-start",
                            paddingLeft: 10,
                            justifyContent: "center",
                            backgroundColor: colors.text_container_bg,
                          }}
                        >
                          <View style={styles.textinput}>
                            <Text
                              style={{
                                fontFamily: regular,
                                fontSize: f_m,
                                color: colors.grey,
                              }}
                            >
                              {data.first_name}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginBottom: 20 }}>
                      <Text
                        style={{
                          color: colors.text_grey,
                          fontSize: f_xs,
                          fontFamily: normal,
                        }}
                      >
                        {t('Last Name')}
                      </Text>
                      <View style={{ margin: 5 }} />
                      <TouchableOpacity
                        activeOpacity={1}
                        onPress={navigate.bind(this, "EditLastName")}
                        style={{ flexDirection: "row" }}
                      >
                        <View
                          style={{
                            width: "15%",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: colors.theme_bg_three,
                          }}
                        >
                          <Icon
                            type={Icons.MaterialIcons}
                            name="person"
                            color={colors.theme_fg_two}
                            style={{ fontSize: 30 }}
                          />
                        </View>
                        <View
                          style={{
                            width: "85%",
                            alignItems: "flex-start",
                            paddingLeft: 10,
                            justifyContent: "center",
                            backgroundColor: colors.text_container_bg,
                          }}
                        >
                          <View style={styles.textinput}>
                            <Text
                              style={{
                                fontFamily: regular,
                                fontSize: f_m,
                                color: colors.grey,
                              }}
                            >
                              {data.last_name}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginBottom: 20 }}>
                      <Text
                        style={{
                          color: colors.text_grey,
                          fontSize: f_xs,
                          fontFamily: normal,
                        }}
                      >
                        {t('Email')}
                      </Text>
                      <View style={{ margin: 5 }} />
                      <TouchableOpacity
                        activeOpacity={1}
                        onPress={navigate.bind(this, "EditEmail")}
                        style={{ flexDirection: "row" }}
                      >
                        <View
                          style={{
                            width: "15%",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: colors.theme_bg_three,
                          }}
                        >
                          <Icon
                            type={Icons.MaterialIcons}
                            name="email"
                            color={colors.theme_fg_two}
                            style={{ fontSize: 30 }}
                          />
                        </View>
                        <View
                          style={{
                            width: "85%",
                            alignItems: "flex-start",
                            paddingLeft: 10,
                            justifyContent: "center",
                            backgroundColor: colors.text_container_bg,
                          }}
                        >
                          <View style={styles.textinput}>
                            <Text
                              style={{
                                fontFamily: regular,
                                fontSize: f_m,
                                color: colors.grey,
                              }}
                            >
                              {data.email}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginBottom: 20 }}>
                      <Text
                        style={{
                          color: colors.text_grey,
                          fontSize: f_xs,
                          fontFamily: normal,
                        }}
                      >
                        {t('Phone Number')}
                      </Text>
                      <View style={{ margin: 5 }} />
                      <View style={{ flexDirection: "row" }}>
                        <View
                          style={{
                            width: "15%",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: colors.theme_bg_three,
                          }}
                        >
                          <Icon
                            type={Icons.MaterialIcons}
                            name="smartphone"
                            color={colors.theme_fg_two}
                            style={{ fontSize: 30 }}
                          />
                        </View>
                        <View
                          style={{
                            width: "85%",
                            alignItems: "flex-start",
                            paddingLeft: 10,
                            justifyContent: "center",
                            backgroundColor: colors.text_container_bg,
                          }}
                        >
                          <View style={styles.textinput}>
                            <Text
                              style={{
                                fontFamily: regular,
                                fontSize: f_m,
                                color: colors.grey,
                              }}
                            >
                              {data.phone_with_code}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </ScrollView>
            </View>
          )}
        </ScrollView>
        <Toast />
      </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    header: {
        height: 70,
        backgroundColor: colors.lite_bg,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textinput: {
        fontSize: f_m,
        color: colors.grey,
        fontFamily: regular,
        height: 50,
        backgroundColor: colors.text_container_bg,
        width: '100%',
        alignItems:"flex-start",
        justifyContent:"center"
    },
});

const mapDispatchToProps = (dispatch) => ({
    updateFirstName: (data) => dispatch(updateFirstName(data))
});

export default connect(null, mapDispatchToProps)(Profile);