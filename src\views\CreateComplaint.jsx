import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useRef } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,
    StatusBar,
    ScrollView,
    TextInput
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import { screenHeight, screenWidth, api_url, add_complaint, bold, regular, btn_loader, f_xl, f_m, f_s, normal } from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import DropShadow from "react-native-drop-shadow";
import LottieView from 'lottie-react-native';
import axios from 'axios';
import { SafeAreaView } from "react-native-safe-area-context";

const CreateComplaint = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const route = useRoute();
    const [loading, setLoading] = useState(false);
    const [subject, setSubject] = useState("");
    const [description, setDescription] = useState("");
    const [trip_id, setTripId] = useState(route.params.trip_id);
    const [complaint_category_id, setComplaintCategoryId] = useState(route.params.complaint_category_id);
    const [complaint_sub_category_id, setComplaintSubCategoryId] = useState(route.params.complaint_sub_category_id);
    const [complaint_category_name, setComplaintCategoryName] = useState(route.params.complaint_category_name);
    const [sub_category_data, setSubCategoryData] = useState(route.params.sub_category_data);
    
    const inputRef = useRef();
    const go_back = () => {
        navigation.goBack();
    }



    const call_validation = () => {
        if(subject == "" || description == ""){
            // dropDownAlertRef({
            //     type: DropdownAlertType.Error,
            //     title: t('Validation error'),
            //     message: t('Please enter all the required fields'),
            //   });
        }else{
            call_add_complaint();
        }
    }

    const call_add_complaint = () => {
        console.log({ trip_id:trip_id, complaint_category:complaint_category_id, complaint_sub_category:complaint_sub_category_id, subject:subject, description:description })
        setLoading(true);
        axios({
            method: 'post',
            url: api_url + add_complaint,
            data: { trip_id:trip_id, complaint_category:complaint_category_id, complaint_sub_category:complaint_sub_category_id, subject:subject, description:description }
        })
            .then(async response => {
                setLoading(false);
                if(response.data.status == 1){
                    navigate_bill();
                    // dropDownAlertRef({
                    //     type: DropdownAlertType.Success,
                    //     title: t('Registered success'),
                    //     message: t('Your complaint registered successfully'),
                    //   });
                }else{
                    // dropDownAlertRef({
                    //     type: DropdownAlertType.Error,
                    //     title:t('Validation error'),
                    //     message:t('Sorry something went wrong'),
                    //   });
                }
            })
            .catch(error => {
                setLoading(false);
                // dropDownAlertRef({
                //     type: DropdownAlertType.Error,
                //     title:t('Validation error'),
                //     message:t('Sorry something went wrong'),
                //   });
            });
    }

    const navigate_bill = () => {
        navigation.navigate("Bill",{trip_id: trip_id})
    }

    return (
      <SafeAreaView style={{ backgroundColor: colors.lite_bg, flex: 1 }}>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle="dark-content"
        />
        <View style={[styles.header]}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={go_back.bind(this)}
            style={{
              width: "15%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon
              type={Icons.MaterialIcons}
              name="arrow-back"
              color={colors.theme_fg_two}
              style={{ fontSize: 30 }}
            />
          </TouchableOpacity>
          <View
            activeOpacity={1}
            style={{
              width: "85%",
              alignItems: "flex-start",
              justifyContent: "center",
            }}
          >
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{
                color: colors.theme_fg_two,
                fontSize: f_xl,
                fontFamily: regular,
              }}
            >
              {t('Complaint')}
            </Text>
          </View>
        </View>

        <View style={{ alignItems: "center", paddingHorizontal: 20 }}>
          <View style={{ margin: 2.5 }} />
          <DropShadow
            style={{
              width: "100%",
              marginBottom: 5,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            }}
          >
            <View
              style={{
                width: "100%",
                backgroundColor: colors.theme_bg_three,
                borderRadius: 10,
                padding: 20,
                marginTop: 5,
                marginBottom: 5,
              }}
            >
              <View
                style={{
                  width: "100%",
                  alignItems: "flex-start",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_m,
                    fontFamily: normal,
                  }}
                >
                  {complaint_category_name} /{" "}
                  {sub_category_data.complaint_sub_category_name}
                </Text>
                <View style={{ margin: 3 }} />
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_s,
                    fontFamily: regular,
                  }}
                >
                  {sub_category_data.short_description}
                </Text>
              </View>
            </View>
          </DropShadow>
          <DropShadow
            style={{
              width: "100%",
              marginBottom: 5,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            }}
          >
            <View
              style={{
                width: "100%",
                backgroundColor: colors.theme_bg_three,
                borderRadius: 10,
                padding: 20,
                marginTop: 5,
                marginBottom: 5,
              }}
            >
              <View
                style={{
                  width: "100%",
                  alignItems: "flex-start",
                  paddingLeft: 10,
                  justifyContent: "center",
                  backgroundColor: colors.text_container_bg,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderStyle: "dotted",
                }}
              >
                <TextInput
                  ref={inputRef}
                  placeholder={t('Subject')}
                  secureTextEntry={false}
                  placeholderTextColor={colors.grey}
                  style={styles.textinput}
                  onChangeText={(TextInputValue) => setSubject(TextInputValue)}
                />
              </View>
              <View style={{ margin: 10 }} />
              <View
                style={{
                  width: "100%",
                  alignItems: "center",
                  paddingLeft: 5,
                  justifyContent: "center",
                  backgroundColor: colors.text_container_bg,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderStyle: "dotted",
                }}
              >
                <TextInput
                  placeholder={t('Enter details about your complaint')}
                  secureTextEntry={false}
                  multiline={true}
                  numberOfLines={10}
                  placeholderTextColor={colors.grey}
                  style={styles.textarea}
                  onChangeText={(TextInputValue) =>
                    setDescription(TextInputValue)
                  }
                />
              </View>
              <View style={{ margin: 10 }} />
              {loading == true ? (
                <View style={{ height: 50, width: "90%", alignSelf: "center" }}>
                  <LottieView source={btn_loader} autoPlay loop />
                </View>
              ) : (
                <TouchableOpacity
                  onPress={call_validation.bind(this)}
                  activeOpacity={1}
                  style={{
                    width: "100%",
                    backgroundColor: colors.btn_color,
                    borderRadius: 10,
                    height: 50,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      color: colors.theme_fg_two,
                      fontSize: f_m,
                      color: colors.theme_fg_three,
                      fontFamily: normal,
                    }}
                  >
                    {t('Submit')}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </DropShadow>
        </View>

      </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        ...StyleSheet.absoluteFillObject,
        height: screenHeight,
        width: screenWidth,
        backgroundColor: colors.lite_bg
    },
    header: {
        height: 60,
        backgroundColor: colors.lite_bg,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textinput: {
        fontSize: f_m,
        color: colors.grey,
        fontFamily: regular,
        height: 60,
        borderRadius:10,
        backgroundColor: colors.text_container_bg,
        width: '100%'
    },
    textarea: {
        fontSize: f_m,
        color: colors.grey,
        fontFamily: regular,
        borderRadius:10,
        padding:10,
        textAlignVertical: 'top',
        backgroundColor: colors.text_container_bg,
        width: '100%'
    },
});

export default CreateComplaint;