import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useEffect, useCallback } from "react";
import {
  Image,
  Text,
  TouchableOpacity,
  View,
  ImageBackground,
  Linking,
  StatusBar,
  TextInput,
  Modal,
} from "react-native";
import { GiftedChat } from "react-native-gifted-chat";
import database from "@react-native-firebase/database";
import { useNavigation } from "@react-navigation/native";
import Sound from "react-native-sound";
import {
  chat_bg,
  chat_bg_2,
  chat_icon,
  f_s,
  f_xl,
  img_url,
  regular,
} from "../config/Constants";
import * as colors from "../assets/css/Colors";
import Icon, { Icons } from "../components/Icons";
import { SafeAreaView } from "react-native-safe-area-context";

const Chat = ({ route }) => {
  const { t, i18n } = useTranslation();
  const { trip_id, user, data } = route.params; // Ensure you pass these parameters
  const [messages, setMessages] = useState([]);
  const navigation = useNavigation();
  const [whoosh, setWhoosh] = useState(null);
  const [inputText, setInputText] = useState("");
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedAvatar, setSelectedAvatar] = useState(null);
  // Load sound once
useEffect(() => {
  console.log("Loading sound...");
  const sound = new Sound("notification.wav", Sound.MAIN_BUNDLE, (error) => {
    if (error) {
      console.log("Failed to load sound", error);
    } else {
      setWhoosh(sound);
      console.log("Sound loaded successfully");

      // Play sound immediately after loading
      sound.play((success) => {
        if (success) {
          console.log("Sound played successfully");
        } else {
          console.log("Sound playback failed");
        }
      });
    }
  });

  return () => {
    if (sound) {
      sound.release();
    }
  };
}, []);

  const go_back = () => {
    navigation.goBack();
  };

const notification_sound = () => {
  if (whoosh) {
    console.log("Sound object is ready: ", whoosh);
    whoosh.play((success) => {
      if (success) {
        console.log("Sound played successfully");
      } else {
        console.log("Sound playback failed");
      }
    });
  } else {
    console.log("Whoosh is not ready yet");
  }
};

  useEffect(() => {
    const ref = database().ref(`/chat/${trip_id}`).limitToLast(20);

    const listener = ref.on("child_added", (snapshot) => {
      const newMessage = parse(snapshot);
      setMessages((previousMessages) =>
        GiftedChat.append(previousMessages, newMessage)
      );
      console.log("New message received:", newMessage); // Debugging log
      notification_sound(); // Play sound when a new message is received
    });

    const _unblur = navigation.addListener("blur", () => {
      if (whoosh) whoosh.stop();
    });

    return () => {
      ref.off("child_added", listener);
      _unblur();
    };
  }, [whoosh]);


  const parse = (snapshot) => {
    const { text, user, createdAt } = snapshot.val();
    const { key: _id } = snapshot;

    console.log("RECEIVED MESSAGE:", snapshot.val()); // Debugging log

    return {
      _id,
      text,
      createdAt: createdAt ? new Date(createdAt) : new Date(),
      user,
    };
  };
  const call_customer = (phone_number, contact) => {
    console.log("phone");
    console.log(phone_number, contact);
    if (contact == "null") {
      Linking.openURL(`tel:${phone_number}`);
    } else {
      Linking.openURL(`tel:${contact}`);
    }
  };
  const onSend = useCallback((messages = []) => {
    messages.forEach((message) => {
      const { text, user } = message;
      const messageData = {
        text,
        user,
        createdAt: database.ServerValue.TIMESTAMP,
      };

      console.log("SENDING MESSAGE:", messageData); // Debugging log

      database().ref(`/chat/${trip_id}`).push(messageData);
    });
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <ImageBackground
        source={chat_bg} // Provide your image URL or local file path here
        style={{ flex: 1, justifyContent: "flex-start",paddingHorizontal:10 }} // Makes sure the background image takes the whole space
      >
        <View
          style={{
            height: 70,
            width: "100%",
            flexDirection: "row",
            alignItems: "center",
            elevation:10
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={go_back}
            style={{
              width: "10%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon
              type={Icons.MaterialIcons}
              name="arrow-back"
              color={colors.theme_bg_two}
              style={{ fontSize: 30 }}
            />
          </TouchableOpacity>
          <View
            style={{
              width: "70%",
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              gap: 10,
            }}
          >
            <View style={{ height: 40, width: 40, borderRadius: 5 }}>
              <Image
                style={{
                  height: undefined,
                  width: undefined,
                  flex: 1,
                  borderRadius: 100/2,
                }}
                source={{ uri: img_url + data.trip.driver.profile_picture }}
              />
            </View>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{
                color: colors.theme_bg_two,
                fontSize: f_xl,
                fontFamily: regular,
              }}
            >
              {data.trip.driver.first_name} {data.trip.driver.last_name}
            </Text>
          </View>
          <TouchableOpacity
            onPress={call_customer.bind(
              this,
              data.trip.driver.phone_with_code,
              data.trip.contact
            )}
            style={{
              width: "20%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon
              type={Icons.MaterialIcons}
              name="call"
              color={colors.theme_fg_two}
              style={{ fontSize: 30 }}
            />
          </TouchableOpacity>
        </View>

        <GiftedChat
          messages={messages}
          onSend={onSend}
          user={{
            _id: global.id + "-Cr",
            name: global.first_name,
            avatar: img_url + global.profile_picture,
          }}
          showAvatarForEveryMessage={true}
          showUserAvatar={true}
          renderAvatar={(props) => (
            <TouchableOpacity
              onPress={() => {
                setSelectedAvatar(props.currentMessage.user.avatar);
                setModalVisible(true);
              }}
            >
              <Image
                source={{ uri: props.currentMessage.user.avatar }}
                style={{ width: 40, height: 40, borderRadius: 20 }}
              />
            </TouchableOpacity>
          )}
          alwaysShowSend
          placeholder={t('Type your message') + "..."}
          renderUsernameOnMessage
          renderSend={(props) => (
            <TouchableOpacity
              onPress={() => {
                if (props.onSend) {
                  props.onSend({ text: inputText });
                  setInputText(""); // Clear input text after sending
                }
              }}
              style={{
                marginRight: 10,
                justifyContent: "center",
                alignItems: "center",
                padding: 5,
              }}
            >
              <Icon
                type={Icons.MaterialIcons}
                name="send"
                color={colors.theme_bg}
                style={{ fontSize: 30 }}
              />
            </TouchableOpacity>
          )}
          renderFooter={() => <View style={{ margin: '10%' }} />}
          renderInputToolbar={(props) => (
            <View
              style={{
                backgroundColor: "#fff",
                borderRadius: 15,
                padding: 5,
                
                flexDirection: "row",
                alignItems: "center",
                // borderColor: "#ddd",
                // borderWidth: 1,
                marginHorizontal: 10,
                position: "absolute",
                bottom: 10,
                elevation: 10,
              }}
            >
              <TextInput
                style={{
                  flex: 1,
                  height: 40,
                  fontSize: f_s,
                  color: "#333",
                  paddingLeft: 10,
                  paddingRight: 10,
                }}
                value={inputText}
                onChangeText={setInputText} // Update state on input change
                placeholder={props.placeholder}
                placeholderTextColor="#aaa"
              />
              <TouchableOpacity
                onPress={() => {
                  if (inputText) {
                    if (props.onSend) {
                      props.onSend({ text: inputText });
                      setInputText(""); // Clear input text after sending
                    }
                  }
                }}
                style={{ marginLeft: 10 }}
              >
                <Icon
                  type={Icons.MaterialIcons}
                  name="send"
                  color={colors.theme_bg}
                  style={{ fontSize: 30 }}
                />
              </TouchableOpacity>
            </View>
          )}
        />
        <Modal
          transparent={true}
          visible={modalVisible}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              justifyContent: "center",
              alignItems: "center",
            }}
            activeOpacity={1}
            onPress={() => setModalVisible(false)} // Close modal on outside tap
          >
            <View
              style={{
                backgroundColor: "transparent",
                padding: 15,          
                alignItems: "center",
              }}
            >
              {selectedAvatar && (
                <Image
                  source={{ uri: selectedAvatar }}
                  style={{ width: 300, height: 300, borderRadius:150}}
                />
              )}
           
            </View>
          </TouchableOpacity>
        </Modal>
      </ImageBackground>
    </SafeAreaView>
  );
};

export default Chat;
