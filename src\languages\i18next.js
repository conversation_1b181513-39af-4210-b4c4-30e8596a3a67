import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

import en from './en.json';
import ar from './ar.json';

export const i18nInitPromise = (async () => {
  const savedLang = await AsyncStorage.getItem('language');
  const selectedLanguage = savedLang || 'en';

  await i18n.use(initReactI18next).init({
    compatibilityJSON: 'v3',
    resources: {
      en: {translation: en},
      ar: {translation: ar},
    },
    lng: selectedLanguage,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

  return true;
})();

export default i18n;
