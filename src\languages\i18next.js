import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

import en from './en.json';
import ar from './ar.json';
import tr from './tr.json';

export const i18nInitPromise = (async () => {
  const savedLang = await AsyncStorage.getItem('language');
  const selectedLanguage = savedLang || 'tr';

  await i18n.use(initReactI18next).init({
    compatibilityJSON: 'v3',
    resources: {
      en: {translation: en},
      ar: {translation: ar},
      tr: {translation: tr},
    },
    lng: selectedLanguage,
    fallbackLng: 'tr',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

  return true;
})();

export default i18n;
