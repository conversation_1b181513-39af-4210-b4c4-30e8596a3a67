import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useEffect, useRef } from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  Image,
  StatusBar,
  FlatList
} from "react-native";
import { useNavigation, CommonActions } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import { screenHeight, screenWidth, bold, get_notification_messages, notification_bell, api_url, regular, loader, f_s, f_xs, f_xl } from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import DropShadow from "react-native-drop-shadow";
import axios from 'axios';
import Moment from 'moment';
import LottieView from 'lottie-react-native';
import Animated, { useAnimatedStyle, withTiming, useSharedValue } from 'react-native-reanimated';
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import moment from 'moment';
import 'moment/locale/ar'; // Arabic locale
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const Notifications = (props) => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const viewableItems = useSharedValue([]);



  useEffect(() => {
    if (i18n.language === 'ar') {
      moment.locale('ar');
    } else {
      moment.locale('en');
    }
    const unsubscribe = navigation.addListener("focus", async () => {
      call_get_notification_messages();
    });
    return (
      unsubscribe
    );
  }, []);
  const showToast = (type, title, message) => {
    ReactNativeHapticFeedback.trigger("impactLight", haptic_option);
    Toast.show({
      type: type ,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: "top", // top, bottom
    });
  };
  const call_get_notification_messages = () => {    
    setLoading(true);
    console.log(api_url + get_notification_messages);
    console.log({ customer_id: global.id, lang: i18n.language });
    axios({
      method: 'post',
      url: api_url + get_notification_messages,
      data: { customer_id: global.id, lang: i18n.language }
    })
      .then(async response => {
        setData(response.data.result)
        setLoading(false);
       
      })
      .catch(error => {
        setLoading(false);
         showToast("error", t('Error'), t('Sorry something went wrong'));
     
      });
  }

  navigate_notification_details = (data) => {
    navigation.navigate('NotificationDetails', { data: data });
  }

  type ListItemProps = {
    viewableItems: Animated.SharedValue<ViewToken[]>;
    item: {
      id: number;
    };
  };

  const ListItem: React.FC<ListItemProps> = React.memo(
    ({ item, viewableItems }) => {
      const rStyle = useAnimatedStyle(() => {
        const isVisible = Boolean(
          viewableItems.value
            .filter((item) => item.isViewable)
            .find((viewableItem) => viewableItem.item.id === item.id)
        );
        return {
          opacity: withTiming(isVisible ? 1 : 0),
          transform: [
            {
              scale: withTiming(isVisible ? 1 : 0.6),
            },
          ],
        };
      }, []);
      return (
        <Animated.View
          style={[
            {
              width: '95%',
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
            },
            rStyle,
          ]}>
          <DropShadow
            style={{
              width: '100%',
              marginBottom: 5,

              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0,
              },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            }}>
            <TouchableOpacity
              onPress={navigate_notification_details.bind(this, item)}
              activeOpacity={1}
              style={{
                flexDirection: 'row',
                flex: 1,
                backgroundColor: colors.theme_bg_three,
                padding: 15,
                marginTop: 5,
                marginBottom: 5,
                borderRadius: 10,
              }}>
              <View style={{width: 50, height: 50}}>
                <Image
                  style={{
                    height: undefined,
                    width: undefined,
                    flex: 1,
                    borderRadius: 10,
                  }}
                  source={notification_bell}
                />
              </View>
              <View style={{margin: 10}} />
              <View
                style={{
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                  width: '80%',
                }}>
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_s,
                    fontFamily: regular,
                  }}>
                  {item.title}
                </Text>
                <View style={{margin: 1}} />
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.text_grey,
                    fontSize: f_xs,
                    fontFamily: regular,
                  }}>
                  {item.message}
                </Text>
                <View style={{margin: 4}} />
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.text_grey,
                    fontSize: f_xs,
                    fontFamily: regular,
                  }}>
                  {Moment(item.created_at).fromNow()}
                </Text>
              </View>
            </TouchableOpacity>
          </DropShadow>
        </Animated.View>
      );
    }
  );

  const onViewableItemsChanged = ({ viewableItems: vItems }) => {
    viewableItems.value = vItems;
  };

  const viewabilityConfigCallbackPairs = useRef([{ onViewableItemsChanged }]);

  return (
    <SafeAreaView style={{ backgroundColor: colors.lite_bg, flex: 1 }}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <View style={[styles.header]}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            navigation.toggleDrawer();
        ReactNativeHapticFeedback.trigger("impactLight", options);

          }}
          style={{
            width: "15%",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Icon
            type={Icons.MaterialIcons}
            name="arrow-back"
            color={colors.theme_fg_two}
            style={{ fontSize: 30 }}
          />
        </TouchableOpacity>
        <View
          activeOpacity={1}
          style={{
            width: "85%",
            alignItems: "flex-start",
            justifyContent: "center",
          }}
        >
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xl,
              fontFamily: regular,
            }}
          >
            {t('Notifications')}
          </Text>
        </View>
      </View>
      {loading == true ? (
        <View
          style={{
            height: 100,
            width: 100,
            alignSelf: "center",
            marginTop: "30%",
          }}
        >
          <LottieView style={{ flex: 1 }} source={loader} autoPlay loop />
        </View>
      ) : (
        <View style={{ marginHorizontal: 10,justifyContent:'center',alignItems:'center' }}>
          <FlatList
            data={data}
            viewabilityConfigCallbackPairs={
              viewabilityConfigCallbackPairs.current
            }
            renderItem={({ item }) => {
              return <ListItem item={item} viewableItems={viewableItems} />;
            }}
          />
        </View>
      )}
                      <Toast />
      
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    height: screenHeight,
    width: screenWidth,
  },
  header: {
    height: 70,
    backgroundColor: colors.lite_bg,
    flexDirection: 'row',
    alignItems: 'center'
  },
});

export default Notifications;