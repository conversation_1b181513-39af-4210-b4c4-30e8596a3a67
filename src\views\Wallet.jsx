import { useTranslation } from "react-i18next";
import '../languages/i18next';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import React, { useState, useEffect, useRef } from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  ScrollView,
  Image,
  StatusBar,
  FlatList,
  Modal,
  TextInput,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import * as colors from "../assets/css/Colors";
import Icon, { Icons } from "../components/Icons";
import {
  normal,
  bold,
  img_url,
  api_url,
  add_wallet,
  no_data_loader,
  income_icon,
  expense_icon,
  payment_methods,
  app_name,
  wallet,
  f_xs,
  f_s,
  f_m,
  f_xl,
  f_30,
  regular,
  f_l,
  FLUTTERWAVE_KEY,
} from "../config/Constants";
import DropShadow from "react-native-drop-shadow";
import RazorpayCheckout from "react-native-razorpay";
import RBSheet from "react-native-raw-bottom-sheet";

import axios from "axios";
import LottieView from "lottie-react-native";
import Moment from "moment";

import { paypalPaymentStatus } from "../actions/PaymentActions";
import { connect } from "react-redux";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";

import { PayWithFlutterwave } from "flutterwave-react-native";
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const Wallet = (props) => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [amount, setAmount] = useState(0);
  const [payment_methods_list, setPaymentMethodsList] = useState([]);
  const wallet_ref = useRef(null);
  const [data, setData] = useState([]);
  const [all, setAll] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [receives, setReceives] = useState([]);
  const [isDialogVisible, setDialogVisible] = useState(false);
  const [wallet_amount, setWalletAmount] = useState(0);
  const [filter, setFilter] = useState(1);
  const [flutterwave_id, setFlutterwaveId] = useState(0);
  const [isModalVisible, setModalVisible] = useState(false);
  const [inputText, setInputText] = useState("");

  const ref_flutterwave_sheet = useRef(null);
  const go_back = () => {
ReactNativeHapticFeedback.trigger("impactLight", options);

    navigation.toggleDrawer();
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", async () => {
      if (props.paypal_payment_status != 0) {
        call_add_wallet();
      }
      call_wallet();
      call_payment_methods();
    });

    return unsubscribe;
  }, []);
  const showToast = (type, title, message) => {
   ReactNativeHapticFeedback.trigger("impactHeavy", options);

    Toast.show({
      type: type ,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: "top", // top, bottom
    });
  };
  const call_add_wallet = () => {
    setLoading(true);
    axios({
      method: "post",
      url: api_url + add_wallet,
      data: { id: global.id, amount: amount },
    })
      .then(async (response) => {
        setLoading(false);
        if (response.data.status == 1) {
          call_wallet();
           showToast(
             "success",
             t('Success'),
             t('Amount successfully added to your wallet')
           );
           setInputText("")
     
          await props.paypalPaymentStatus(0);
        } else {
           showToast("error", t('Error'), response.data.message);
    
        }
      })
      .catch((error) => {
        setLoading(false);
         showToast("error", t('Error'), t('Sorry something went wrong'));
      });
  };

  const call_payment_methods = () => {
    setLoading(true);
    axios({
      method: "post",
      url: api_url + payment_methods,
      data: { lang: i18n.language },
    })
      .then(async (response) => {
        setLoading(false);
        if (props.paypal_payment_status != 0) {
          call_add_wallet(props.paypal_payment_status);
        } else if (response.data.status == 1) {
          setPaymentMethodsList(response.data.result);
        }

      })
      .catch((error) => {
        setLoading(false);
         showToast("error", t('Error'), t('Sorry something went wrong'));
      });
  };

  const choose_payment = async (total_fare) => {
   
    if (total_fare == "" || total_fare == undefined || total_fare == 0) {
       showToast("error", t('Error'), t('Please enter valid amount'));
    } else {
      setDialogVisible(false);
      setModalVisible(false)
      setAmount(total_fare);
      await wallet_ref.current.open();
                 setInputText("");
    }
  };

  const select_payment = async (item) => {
    await payment_done(item.id);
    await wallet_ref.current.close();
  };

  const payment_done = async (payment_id) => {
    if (payment_id != 0) {
      if (payment_id == 5) {
        await call_razorpay();
      } else if (payment_id == 6) {
        navigate_paypal(payment_id);
      }else if (payment_id == 7) {
        ref_flutterwave_sheet.current.open();
      }
    } else {
       showToast("error", t('Error'), t('Sorry something went wrong'));
    }
  };

  const navigate_paypal = () => {
    navigation.navigate("Paypal", { amount: amount,from:"Wallet" });
  };

  const open_dialog = () => {
    setModalVisible(true);
    // setDialogVisible(true);
  };

  const close_dialogbox = () => {
    setDialogVisible(false);
  };
  const call_razorpay = async () => {
    var options = {
      currency: global.currency_short_code,
      key: global.razorpay_key,
      amount: amount * 100,
      name: app_name,
      prefill: {
        contact: global.phone_with_code,
        name: global.first_name,
        email: global.email,
      },
      theme: { color: colors.theme_fg },
    };
    RazorpayCheckout.open(options)
      .then((data) => {
        console.log("Payment Success:", data);
        call_add_wallet();
      })
      .catch((error) => {
        console.error("Payment Failed:", error.code, error.description);
         showToast("error", t('Error'), t('Transaction declined'));
      });
  };

  const change_filter = (id) => {
    ReactNativeHapticFeedback.trigger("impactLight", options);
    setFilter(id);
  
    console.log("all",all)
    if (id == 1) {
      setData(all);
      console.log("data",data)
    } else if (id == 2) {
      setData(expenses);
    } else if (id == 3) {
      setData(receives);
    }
  };
 const handleOnRedirect = (data) => {
   ref_flutterwave_sheet.current.close();
   if (data.status == "successful") {
     call_add_wallet();
   } else {
     showToast("error", t('Failed'), t('Payment failed'));
  
   }
 };
  const closeModal = () => {
    setModalVisible(false);
    setInputText(""); // Optionally reset the input text
  };
  const call_wallet = () => {
    setLoading(true);
    console.log(api_url + wallet);
    console.log({ id: global.id, lang: i18n.language });
    axios({
      method: "post",
      url: api_url + wallet,
      data: { id: global.id, lang: i18n.language },
    })
      .then(async (response) => {
        setLoading(false);
        setWalletAmount(response.data.result.wallet);
        setAll(response.data.result.all);
        setExpenses(response.data.result.expenses);
        setReceives(response.data.result.receives);
        setFilter(1);
        setData(response.data.result.all);
      })
      .catch((error) => {
        setLoading(false);
         showToast("error", t('Error'), t('Sorry something went wrong'));
      });
  };

  const show_list = ({ item }) => (
    <View
      style={{
        flexDirection: "row",
        width: "100%",
        marginTop: 10,
        marginBottom: 10,
      }}
    >
      <View
        style={{
          width: "20%",
          alignItems: "flex-start",
          justifyContent: "center",
        }}
      >
        {item.type == 1 ? (
          <View style={{ height: 40, width: 40 }}>
            <Image
              source={income_icon}
              style={{ flex: 1, height: undefined, width: undefined }}
            />
          </View>
        ) : (
          <View style={{ height: 40, width: 40 }}>
            <Image
              source={expense_icon}
              style={{ flex: 1, height: undefined, width: undefined }}
            />
          </View>
        )}
      </View>
      <View
        style={{
          width: "50%",
          alignItems: "flex-start",
          justifyContent: "center",
        }}
      >
        <Text
          style={{
            color: colors.text_grey,
            fontSize: f_xs,
            fontFamily: normal,
          }}
        >
          {Moment(item.created_at).format("DD-MMM-YYYY")}
        </Text>
        <View style={{ margin: 2 }} />
        <Text
          style={{
            color: colors.theme_fg_two,
            fontSize: f_s,
            fontFamily: regular,
          }}
        >
          {item.message}
        </Text>
      </View>
      <View
        style={{ width: "30%", alignItems: "center", justifyContent: "center" }}
      >
        {item.type == 1 ? (
          <Text
            style={{ color: colors.success, fontSize: f_s, fontFamily: normal }}
          >
            + {global.currency}
            {item.amount}
          </Text>
        ) : (
          <Text
            style={{ color: colors.error, fontSize: f_s, fontFamily: normal }}
          >
            - {global.currency}
            {item.amount}
          </Text>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={{flex: 1}}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />

      <View style={[styles.header]}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={go_back.bind(this)}
          style={{
            width: '15%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Icon
            type={Icons.MaterialIcons}
            name="arrow-back"
            color={colors.theme_fg_two}
            style={{fontSize: 30}}
          />
        </TouchableOpacity>
        <View
          activeOpacity={1}
          style={{
            width: '85%',
            alignItems: 'flex-start',
            justifyContent: 'center',
          }}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xl,
              fontFamily: regular,
            }}>
            {t('Wallet')}
          </Text>
        </View>
      </View>
      <View style={{zIndex: 1000}}>
        <Toast />
      </View>
      <View style={{alignItems: 'center', paddingHorizontal: 20}}>
        <DropShadow
          style={{
            width: '100%',
            marginBottom: 5,

            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 0,
            },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}>
          <View
            style={{
              width: '100%',
              backgroundColor: colors.theme_bg_three,
              borderRadius: 10,
              padding: 20,
            }}>
            <View style={{flexDirection: 'row'}}>
              <View
                style={{
                  width: '15%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Icon
                  type={Icons.MaterialIcons}
                  name="credit-card"
                  color={colors.theme_fg_two}
                  style={{fontSize: 30}}
                />
              </View>
              <View
                style={{
                  width: '55%',
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                }}>
                <Text
                  numberOfLines={2}
                  ellipsizeMode="tail"
                  style={{
                    color: colors.text_grey,
                    fontSize: f_s,
                    fontFamily: normal,
                  }}>
                  {t('Total wallet balance')}
                </Text>
              </View>
              <TouchableOpacity
                activeOpacity={1}
                onPress={open_dialog}
                style={{
                  width: '30%',
                  alignItems: 'flex-end',
                  justifyContent: 'center',
                }}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{
                    color: colors.theme_fg,
                    fontSize: f_xs,
                    fontFamily: normal,
                  }}>
                  {t('Top up')} +
                </Text>
              </TouchableOpacity>
              <View
                style={{
                  height: 2,
                  borderBottomWidth: 1,
                  borderColor: colors.grey,
                }}
              />
            </View>
            <View
              style={{
                height: 10,
                borderBottomWidth: 1,
                borderColor: colors.grey,
                width: '85%',
                alignSelf: 'flex-end',
                borderStyle: 'dotted',
                marginBottom: 10,
              }}
            />
            <View style={{flexDirection: 'row'}}>
              <View
                style={{
                  marginLeft: '15%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_30,
                    fontFamily: normal,
                    letterSpacing: 1,
                  }}>
                  {global.currency}
                  {wallet_amount}
                </Text>
              </View>
            </View>
          </View>
        </DropShadow>
      </View>
      <View style={{padding: 20}}>
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{
            color: colors.text_grey,
            fontSize: f_s,
            fontFamily: normal,
          }}>
          {t('Transactions List')}
        </Text>
        <View style={{margin: 10}} />
        <View
          style={{
            alignItems: 'center',
            marginVertical: 16,
            paddingHorizontal: 0,
          }}>
          <View
            style={{
              width: '100%',
              maxWidth: 500,
              // backgroundColor: colors.theme_bg_three,
              borderRadius: 12,
              padding: 6,
           
            }}>
            <SegmentedControl
              values={[t('All'), t('Expenses'), t('Receives')]}
              selectedIndex={filter - 1}
              onChange={event => {
                const selectedIndex = event.nativeEvent.selectedSegmentIndex;
                change_filter(selectedIndex + 1);
              }}
              tintColor={colors.btn_color}
              fontStyle={{
                fontFamily: regular,
                fontSize: 14,
                color: colors.text_grey,
                letterSpacing: 0.25,
              }}
              activeFontStyle={{
                fontFamily: bold,
                fontSize: 14,
                color: colors.theme_bg_three,
                letterSpacing: 0.25,
              }}
              backgroundColor="transparent"
              style={{
                height: 40,
                borderWidth: 0,
              }}
              activeTabStyle={{
                backgroundColor: colors.btn_color,
                borderRadius: 8,
                shadowColor: colors.btn_color,
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.2,
                shadowRadius: 3,
              }}
              tabsContainerStyle={{
                justifyContent: 'space-around',
              }}
              tabStyle={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 6,
              }}
            />
          </View>
        </View>

        <View style={{margin: 10}} />
        <View style={{}}>
          {data.length > 0 ? (
            <FlatList
              data={data}
              renderItem={show_list}
              showsVerticalScrollIndicator={false}
              keyExtractor={item => item.id}
              ListFooterComponent={<View style={{margin: '50%'}} />}
            />
          ) : (
            <View style={{height: 300, width: 300, alignSelf: 'center'}}>
              <LottieView
                style={{flex: 1}}
                source={no_data_loader}
                autoPlay
                loop
              />
            </View>
          )}
        </View>
      </View>

      <RBSheet
        ref={wallet_ref}
        height={300}
        animationType="fade"
        duration={250}>
        <View
          style={{
            padding: 20,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'white', // Necessary for the shadow to be visible
            shadowColor: '#000', // Black shadow color
            shadowOffset: {width: 0, height: 2}, // Position of the shadow
            shadowOpacity: 0.2, // Opacity of the shadow
            shadowRadius: 3, // Blur radius of the shadow
            elevation: 3, // Elevation for Android (height of the shadow)
          }}>
          <Text
            style={{
              fontSize: f_l,
              fontFamily: regular,
              color: colors.theme_bg_two,
            }}>
            {t('Choose Your Payment Type')}
          </Text>
        </View>

        <FlatList
          data={payment_methods_list}
          renderItem={({item, index}) => (
            <TouchableOpacity
              style={{flexDirection: 'row', padding: 20}}
              onPress={select_payment.bind(this, item)}>
              <View style={{width: 50}}>
                <Image
                  style={{flex: 1, height: 35, width: 35}}
                  source={{uri: img_url + item.icon}}
                />
              </View>
              <View
                style={{
                  width: '80%',
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    fontFamily: normal,
                    fontSize: f_m,
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    color: colors.theme_fg_two,
                  }}>
                  {item.payment}
                </Text>
              </View>
            </TouchableOpacity>
          )}
          keyExtractor={item => item.id}
        />
      </RBSheet>
      <RBSheet
        ref={ref_flutterwave_sheet}
        height={200}
        openDuration={250}
        closeOnDragDown={true}
        closeOnPressMask={true}
        customStyles={{
          container: [styles.sheetContainer, {padding: 20}],
          draggableIcon: styles.draggableIcon,
        }}>
        <View style={{margin: 10}} />
        <PayWithFlutterwave
          onRedirect={handleOnRedirect}
          options={{
            tx_ref: Date.now() + '-' + global.id,
            authorization: FLUTTERWAVE_KEY,
            customer: {
              email: global.email,
            },
            amount: parseFloat(amount) || 0,
            currency: global.currency_short_code,
            payment_options: 'card',
          }}
        />
      </RBSheet>
      <Modal
        visible={isModalVisible}
        animationType="fade"
        transparent={true}
        onRequestClose={closeModal}
        statusBarTranslucent={true}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0,0,0,0.6)',
            padding: 20,
          }}>
          <View
            style={{
              width: '100%',
              maxWidth: 400,
              backgroundColor: '#fff',
              borderRadius: 16,
              padding: 24,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 4},
              shadowOpacity: 0.1,
              shadowRadius: 20,
              elevation: 5,
            }}>
            {/* Modal Header */}
            <View style={{marginBottom: 20}}>
              <Text
                style={{
                  fontSize: 20,
                  fontWeight: '600',
                  color: '#222',
                  textAlign: 'center',
                  marginBottom: 8,
                }}>
                {t('Add Wallet')}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: '#666',
                  textAlign: 'center',
                  lineHeight: 20,
                }}>
                {t('Please enter your amount')}
              </Text>
            </View>

            {/* Input Field */}
            <TextInput
              style={{
                height: 50,
                borderWidth: 1,
                borderColor: '#e0e0e0',
                borderRadius: 8,
                paddingHorizontal: 16,
                fontSize: 16,
                marginBottom: 24,
                backgroundColor: '#f9f9f9',
              }}
              placeholder={t('Enter Amount')}
              placeholderTextColor="#999"
              value={inputText}
              keyboardType="numeric"
              onChangeText={text => setInputText(text)}
            />

            {/* Action Buttons */}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                gap: 12,
              }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  height: 48,
                  borderRadius: 8,
                  backgroundColor: '#f0f0f0',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={closeModal}>
                <Text
                  style={{
                    color: '#333',
                    fontSize: 16,
                    fontWeight: '500',
                  }}>
                  {t('Cancel')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flex: 1,
                  height: 48,
                  borderRadius: 8,
                  backgroundColor: colors.theme_bg,
                  justifyContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {width: 0, height: 2},
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  elevation: 2,
                }}
                onPress={() => choose_payment(inputText)}>
                <Text
                  style={{
                    color: '#fff',
                    fontSize: 16,
                    fontWeight: '500',
                  }}>
                  {t('Submit')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 70,
    backgroundColor: colors.lite_bg,
    flexDirection: "row",
    alignItems: "center",
  },
  segment_active_bg: {
    padding: 5,
    width: 100,
    backgroundColor: colors.theme_bg,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 10,
  },
  segment_active_fg: {
    color: colors.theme_fg_three,
    fontSize: 14,
    fontFamily: normal,
  },
  segment_inactive_bg: {
    padding: 5,
    width: 100,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 10,
  },
  segment_inactive_fg: {
    color: colors.text_grey,
    fontSize: 14,
    fontFamily: normal,
  },
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.2)",
  },
  modalContent: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    width: 300,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color:colors.theme_bg_two
  },
  modalMessage: {
    marginVertical: 10,
    color: "gray",
  },
  textInput: {
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    width: "100%",
    marginBottom: 20,
    paddingLeft: 10,
    borderRadius: 5,
    color:colors.text_grey
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  button: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    width: "48%",
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
});

function mapStateToProps(state) {
  
  return {
    paypal_payment_status: state.payment.paypal_payment_status,
  };
}

const mapDispatchToProps = (dispatch) => ({
  paypalPaymentStatus: (data) => dispatch(paypalPaymentStatus(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(Wallet);
