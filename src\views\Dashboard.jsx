import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useEffect, useRef } from "react";
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  Image,
  ScrollView,
  Animated  ,
  Keyboard,
  FlatList,
  TextInput,
  StatusBar,
  SafeAreaView
} from "react-native";
import { connect } from 'react-redux';
import { useNavigation } from "@react-navigation/native";
import {
  screenHeight, screenWidth, trip_details, search_loader, normal, promo_codes, bold, GOOGLE_KEY, month_names,
  money_icon, discount_icon, no_favourites, add_favourite, get_home, api_url, img_url, get_estimation_fare, pin_marker,
  regular, get_zone, btn_loader, ride_confirm, trip_request_cancel, f_m, get_recent_searches,
  f_s,
  f_xs,
  f_tiny,
  f_l,
  f_xl
} from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import * as geofire from 'geofire-common';
import * as colors from '../assets/css/Colors';
import DropShadow from "react-native-drop-shadow";
import { Badge, Divider } from 'react-native-paper';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import axios from "axios";
import LottieView from 'lottie-react-native';
import RBSheet from "react-native-raw-bottom-sheet";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import {getDatabase, ref, onValue, set} from '@react-native-firebase/database';
import Modal from "react-native-modal";
import {getMessaging, onMessage} from '@react-native-firebase/messaging';
import notifee, { AndroidImportance } from '@notifee/react-native';
import Geolocation from "@react-native-community/geolocation";
import Toast from "react-native-toast-message";
import {getApp} from '@react-native-firebase/app';
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};


import { useSafeAreaInsets, SafeAreaProvider } from 'react-native-safe-area-context';
import NetInfo from "@react-native-community/netinfo";
import AsyncStorage from "@react-native-async-storage/async-storage";

const Dashboard = (props) => {
  const { t, i18n } = useTranslation();
  const insets = useSafeAreaInsets();
  const isNotchDevice = insets.top > 20;
  const navigation = useNavigation();
  const search = useRef();
  const map_ref = useRef();
  const inputRef = useRef();
  const fav_RBSheet = useRef();
  const add_contact_RBSheet = useRef();
  const [on_loaded, setOnLoaded] = useState(0);
  const [has_interacted, setHasInteracted] = useState(false);
  const [active_location, setActiveLocation] = useState(1);
  const [region, setRegion] = useState(props.initial_region);
  const [trip_types, setTripTypes] = useState([]);
  const [promo_list, setPromoList] = useState([]);
  const [promo, setPromo] = useState(0);
  const [trip_sub_types, setTripSubTypes] = useState([]);
  const [estimation_fares, setEstimationFares] = useState([]);
  const [customer_favourites, setCustomerFavourties] = useState([]);
  const [customer_recent_places, setCustomerRecentPlaces] = useState([]);
  const [active_trip_type, setActiveTripType] = useState(0);
  const [active_trip_sub_type, setActiveTripSubType] = useState(0);
  const [active_vehicle_type, setActiveVehicleType] = useState(0);
  const [loading, setLoading] = useState(false);
  const [current_location_status, setCurrentLocationStatus] = useState(true);
  const [is_date_picker_visible, setDatePickerVisibility] = useState(false);
  const [pickup_date, setPickupDate] = useState(new Date());
  const [pickup_date_label, setPickupDateLabel] = useState(t('Now'));
  const [packages, setPackages] = useState([]);
  const [package_hr, setPackageHr] = useState(0);
  const [package_km, setPackageKm] = useState(0);
  const [package_id, setPackageId] = useState(0);
  const [is_mount, setIsMount] = useState(0);
  const [km, setKm] = useState(0);
  const [search_status, setSearchStatus] = useState(0);
  const [wallet, setWallet] = useState(0);
  const [is_modal_visible, setModalVisible] = useState(false);
  const duration = 500;
  const [trip_request_id, setTripRequestId] = useState(0);
  const [contact_number, setContactNumber] = useState('');
  const [search_loading, setSearchLoading] = useState(false);
  const [userLocation, setUserLocation] = useState(null);


  //Address
  const [pickup_address, setPickupAddress] = useState('');
  const [pickup_lat, setPickupLat] = useState(props.initial_lat);
  const [pickup_lng, setPickupLng] = useState(props.initial_lng);

  const [drop_address, setDropAddress] = useState('');
  const [drop_lat, setDropLat] = useState(0);
  const [drop_lng, setDropLng] = useState(0);

  const [tmp_address, setTmpAddress] = useState('');
  const [tmp_lat, setTmpLat] = useState(props.initial_lat);
  const [tmp_lng, setTmpLng] = useState(props.initial_lng);
  const hasInitialized = useRef(false);
  //Screen Home
  const home_comp_1 = useRef(new Animated.Value(-110)).current;
  const home_comp_2 = useRef(new Animated.Value(screenHeight + 190)).current;

  //Screen Location
  const drop_comp_1 = useRef(new Animated.Value(-110)).current;
  const drop_comp_2 = useRef(new Animated.Value(screenHeight + 150)).current;
  const drop_comp_3 = useRef(new Animated.Value(-130)).current;
  const drop_comp_4 = useRef(
    new Animated.Value(screenHeight + (screenHeight - 100)),
  ).current;

  //Screen Booking
  const book_comp_1 = useRef(new Animated.Value(screenHeight + 250)).current;
  const app = getApp();

  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
    

      screen_home_entry();
      handle_region_change_complete({
        latitude: props.initial_lat,
        longitude: props.initial_lng,
      });
      get_home_api();
      booking_sync();
      call_promo_codes();
      view_recent_places();
    }

    const unsubscribeFocus = navigation.addListener('focus', () => {
      setIsMount(1);
    });

    return () => {
      unsubscribeFocus();
    };
  }, []);


 
  useEffect(() => {
    const checkConnection = () => {
      NetInfo.fetch().then(state => {
        if (!state.isConnected) {
          ReactNativeHapticFeedback.trigger('impactLight', options);
          navigation.navigate('NoInternet');
        }
      });
    };
    checkConnection();
    const interval = setInterval(checkConnection, 10000);
    return () => clearInterval(interval);
  }, []);

  const call_promo_codes = () => {
    axios({
      method: 'post',
      url: api_url + promo_codes,
      data: {lang: i18n.language, customer_id: global.id},
    })
      .then(async response => {
        setPromoList(response.data.result);
      })
      .catch(error => {
        console.log(error);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };
  const showToast = (type, title, message) => {
    ReactNativeHapticFeedback.trigger('impactHeavy', options);
    Toast.show({
      type: type,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: 'top', // or 'bottom'
      topOffset: 120, // optional: controls how far from top
    });
  };
  const call_apply_promo = data => {
    setPromo(data.id);
    toggleModal();
    get_estimation_fare_api(
      pickup_lat,
      pickup_lng,
      drop_lat,
      drop_lng,
      package_id,
      active_trip_sub_type,
      data.id,
    );
  };

const booking_sync = () => {
  const db = getDatabase();
  const userRef = ref(db, `customers/${global.id}`);

  onValue(userRef, snapshot => {
    if (snapshot.exists()) {
      const data = snapshot.val();
      setSearchStatus(data.is_searching);

      if (data.booking_id !== 0) {
        if (is_mount === 0) {
          setIsMount(1);
          booking_exit();
          setActiveTripType(1);
          call_trip_details(data.booking_id);
        }
      }
    }
  });
};
  const centerToUserLocation = () => {
    if (props.initial_lat && props.initial_lng && map_ref.current) {
      ReactNativeHapticFeedback.trigger('impactHeavy', options);
      map_ref.current.animateToRegion({
        ...region,
        latitude:props.initial_lat,
        longitude:props.initial_lng,
      });
    }
  };
  const call_trip_details = trip_id => {
    axios({
      method: 'post',
      url: api_url + trip_details,
      data: {trip_id: trip_id},
    })
      .then(async response => {
        navigation.navigate('TripDetails', {
          trip_id: trip_id,
          from: 'home',
          data: response.data.result,
        });
      })
      .catch(error => {
        console.log(error);
      });
  };

  const toggleModal = () => {
    setModalVisible(!is_modal_visible);
  };





  

  const set_default_date = async (currentdate, type) => {
    let datetime =
      (await (currentdate.getDate() < 10 ? '0' : '')) +
      currentdate.getDate() +
      '-' +
      (currentdate.getMonth() + 1 < 10 ? '0' : '') +
      (currentdate.getMonth() + 1) +
      '-' +
      currentdate.getFullYear() +
      ' ' +
      (currentdate.getHours() < 10 ? '0' : '') +
      currentdate.getHours() +
      ':' +
      (currentdate.getMinutes() < 10 ? '0' : '') +
      currentdate.getMinutes() +
      ':' +
      (currentdate.getSeconds() < 10 ? '0' : '') +
      currentdate.getSeconds();
    let label =
      (await (currentdate.getDate() < 10 ? '0' : '')) +
      currentdate.getDate() +
      ' ' +
      month_names[currentdate.getMonth()] +
      ', ' +
      formatAMPM(currentdate);
    if (type == 0) {
      setPickupDateLabel(t('Now'));
    } else {
      setPickupDateLabel(label);
    }

    setPickupDate(datetime);
  };

  const formatAMPM = date => {
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12;
    minutes = minutes < 10 ? '0' + minutes : minutes;
    var strTime = hours + ':' + minutes + ' ' + ampm;
    return strTime;
  };

  const screen_home_entry = () => {
    Keyboard.dismiss();
    Animated.timing(home_comp_1, {
      toValue: 60,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(home_comp_2, {
      toValue: screenHeight,
      duration: duration,
      useNativeDriver: true,
    }).start();
    setPromo(0);
  };

  const screen_home_exit = () => {
    Animated.timing(home_comp_1, {
      toValue: -110,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(home_comp_2, {
      toValue: screenHeight + 190,
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const location_entry = () => {
    Animated.timing(drop_comp_1, {
      toValue: 60,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(drop_comp_2, {
      toValue: screenHeight,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(drop_comp_3, {
      toValue: 0,
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const location_exit = () => {
    Animated.timing(drop_comp_1, {
      toValue: -110,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(drop_comp_2, {
      toValue: screenHeight + 150,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(drop_comp_3, {
      toValue: -130,
      duration: duration,
      useNativeDriver: true,
    }).start();
    Animated.timing(drop_comp_4, {
      toValue: screenHeight + (screenHeight - 100),
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const search_entry = () => {
    Animated.timing(drop_comp_4, {
      toValue: 0,
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const search_exit = () => {
    Keyboard.dismiss();
    Animated.timing(drop_comp_4, {
      toValue: screenHeight + (screenHeight - 100),
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const booking_entry = () => {
    location_exit();
    set_default_date(new Date(), 0);
    setCurrentLocationStatus(false);
    Animated.timing(book_comp_1, {
      toValue: 250,
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const booking_exit = () => {
    ReactNativeHapticFeedback.trigger('impactLight', options);

    setCurrentLocationStatus(true);
    screen_home_entry();
    Animated.timing(book_comp_1, {
      toValue: screenHeight + 250,
      duration: duration,
      useNativeDriver: true,
    }).start();
  };

  const is_focus = () => {
    search_entry();
  };

  const handle_region_change_complete = region => {
    if (on_loaded === 1) {
      setHasInteracted(true);
    }
   
    region_change(region);
  };

  const region_change = region => {

    if (on_loaded == 1 ) {
   

      screen_home_exit();
      location_entry();
      onRegionChange(region, 'T');
    } else {
      setPickupAddress(t('Loading') + '...');
      onRegionChange(region, 'P');
    }
  };

  const onRegionChange = async (value, type) => {
    fetch(
      'https://maps.googleapis.com/maps/api/geocode/json?address=' +
        value.latitude +
        ',' +
        value.longitude +
        '&key=' +
        GOOGLE_KEY,
    )
      .then(response => response.json())
      .then(async responseJson => {
        if (responseJson.results[2].formatted_address != undefined) {
          if (type == 'P') {
            setPickupAddress(responseJson.results[2].formatted_address);
            setPickupLat(value.latitude);
            setPickupLng(value.longitude);
          } else {
            setTmpAddress(responseJson.results[2].formatted_address);
            setTmpLat(value.latitude);
            setTmpLng(value.longitude);
            search.current?.setAddressText(
              responseJson.results[2].formatted_address,
            );
          }
        }
      });
  };
  const confirm_location = async () => {
    if (active_location == 1) {
      setPickupAddress(tmp_address);
      setPickupLat(tmp_lat);
      setPickupLng(tmp_lng);
    } else {
      setDropAddress(tmp_address);
      setDropLat(tmp_lat);
      setDropLng(tmp_lng);
    }

    const isSameLocation =
      parseFloat(pickup_lat).toFixed(6) === parseFloat(tmp_lat).toFixed(6) &&
      parseFloat(pickup_lng).toFixed(6) === parseFloat(tmp_lng).toFixed(6);

    if (active_location == 2 && pickup_address !== '') {
      if (isSameLocation) {
        showToast(
          'error',
          t('Error'),
          t('Pickup and Drop location cannot be the same'),
        );
        return;
      }
      booking_entry();
      get_estimation_fare_api(
        pickup_lat,
        pickup_lng,
        tmp_lat,
        tmp_lng,
        0,
        active_trip_sub_type,
        0,
      );
    } else if (active_location == 1 && drop_address !== '') {
      const isSame =
        parseFloat(tmp_lat).toFixed(6) === parseFloat(drop_lat).toFixed(6) &&
        parseFloat(tmp_lng).toFixed(6) === parseFloat(drop_lng).toFixed(6);

      if (isSame) {
         showToast(
           'error',
           t('Error'),
           t('Pickup and Drop location cannot be the same'),
         );
   
        return;
      }
      booking_entry();
      get_estimation_fare_api(
        tmp_lat,
        tmp_lng,
        drop_lat,
        drop_lng,
        0,
        active_trip_sub_type,
        0,
      );
    } else {
      back_to_home_screen();
    }
  };


  const select_package = data => {
    screen_home_exit();
    setPackageId(data.id);
    setPackageHr(data.hours);
    setPackageKm(data.kilometers);
    booking_entry();
    get_estimation_fare_api(
      tmp_lat,
      tmp_lng,
      drop_lat,
      drop_lng,
      data.id,
      0,
      0,
    );
    console.log(tmp_lat, tmp_lng, drop_lat, drop_lng, data.id, 0, 0);
  };
  const get_location = (data, details, type) => {
    search_exit();
    setTmpAddress(data.description);
    setTmpLat(details.geometry.location.lat);
    setTmpLng(details.geometry.location.lng);
    set_location(details.geometry.location.lat, details.geometry.location.lng);
  };

  const set_location = (lat, lng) => {
    map_ref?.current?.animateCamera(
      {
        center: {
          latitude: lat,
          longitude: lng,
        },
      },
      {duration: 2000},
    );
  };

  const back_to_home_screen = () => {
    location_exit();
    screen_home_entry();
    console.log;
  };

  const change_address = change_location => {
    location_exit();
    screen_home_entry();
    open_location(change_location);
  };

  const open_location = async location => {
    //view_recent_places();
    search.current?.setAddressText('');
    search_entry();
    setActiveLocation(location);
    screen_home_exit();
    location_entry();
  };

  const renderTripType = ({item: data}) => {
    const icon =
      data.id === active_trip_type ? data.active_icon : data.inactive_icon;

    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => change_trip_type(data)}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          width: 80,
        }}>
        <View
          style={
            data.id === active_trip_type
              ? styles.active_vehicle_img
              : styles.vehicle_img
          }>
          <Image
            style={{height: undefined, width: undefined, flex: 1}}
            source={{uri: img_url + icon}}
          />
        </View>
        <View style={{margin: 2}} />
        <Text
          style={
            data.id === active_trip_type
              ? styles.active_trip_type_label
              : styles.inactive_trip_type_label
          }>
          {data.name}
        </Text>
      </TouchableOpacity>
    );
  };

  const estimation_fare_list = () => {
    return estimation_fares.map((data, i) => {
      return (
        <DropShadow
          key={i}
          style={{
            width: '100%',
            marginBottom: 5,
            marginTop: 5,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 0,
            },
            shadowOpacity: active_vehicle_type == data.id ? 0.3 : 0,
            shadowRadius: 3,
          }}>
          <TouchableOpacity
            key={i}
            activeOpacity={1}
            onPress={change_vehicle_type.bind(this, data.id)}
            style={{
              width: '100%',
              backgroundColor: colors.theme_bg_three,
              padding: 10,
              flexDirection: 'row',
              borderRadius: 10,
            }}>
            <View
              style={{
                width: '25%',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View style={{height: 30, width: 42}}>
                <Image
                  style={{height: undefined, width: undefined, flex: 1}}
                  source={{uri: img_url + data.active_icon}}
                />
              </View>
              {data.eta != 0 ? (
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: 10,
                    fontFamily: regular,
                  }}>
                  {data.eta}
                </Text>
              ) : (
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: 10,
                    fontFamily: regular,
                  }}>
                  {t('Not Available')}
                </Text>
              )}
            </View>
            <View
              style={{
                width: '50%',
                alignItems: 'flex-start',
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={1}
                style={{
                  color: colors.theme_fg_two,
                  fontSize: f_s,
                  fontFamily: regular,
                }}>
                {data.vehicle_type}
              </Text>
              <View style={{margin: 2}} />
              <Text
                numberOfLines={1}
                style={{
                  color: colors.text_grey,
                  fontSize: f_tiny,
                  fontFamily: regular,
                }}>
                {data.description}
              </Text>
            </View>
            <View
              style={{
                width: '25%',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={{
                  color: colors.theme_fg_two,
                  fontSize: f_xs,
                  fontFamily: normal,
                  letterSpacing: 1,
                }}>
                {global.currency}
                {data.fares.total_fare}
              </Text>
              {promo != 0 && (
                <View
                  style={{
                    marginTop: 4,
                    backgroundColor: colors.success_background,
                    borderRadius: 5,
                    padding: 2,
                    paddingLeft: 5,
                    paddingRight: 5,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    ellipsizeMode="tail"
                    style={{
                      color: colors.success,
                      fontSize: 8,
                      fontFamily: normal,
                    }}>
                    {t('Promo Applied')}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        </DropShadow>
      );
    });
  };

  const load_location = (lat, lng) => {
    // console.log('r'+lat + '-' + lng)
    back_to_home_screen();
    set_location(parseFloat(lat), parseFloat(lng));
  };

  const favourites_list = () => {
    if (customer_favourites.length == 0) {
      return (
        <View
          style={{
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View style={{height: 150, width: 150, alignSelf: 'center'}}>
            <LottieView
              style={{flex: 1}}
              source={no_favourites}
              autoPlay
              loop
            />
          </View>
          <Text
            numberOfLines={2}
            ellipsizeMode="tail"
            style={{
              fontSize: f_xs,
              color: colors.text_grey,
              fontFamily: regular,
            }}>
            {t('No data found')}
          </Text>
        </View>
      );
    } else {
      return customer_favourites.map((data, i) => {
        return (
          <TouchableOpacity
            key={i}
            activeOpacity={1}
            onPress={load_location.bind(this, data.lat, data.lng)}
            style={{
              width: '100%',
              flexDirection: 'row',
              borderBottomWidth: 0.5,
              paddingBottom: 10,
              paddingTop: 10,
            }}>
            <View
              style={{
                width: '15%',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Icon
                type={Icons.MaterialIcons}
                name="near-me"
                color={colors.icon_inactive_color}
                style={{fontSize: 22}}
              />
            </View>
            <View
              style={{
                width: '85%',
                alignItems: 'flex-start',
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={2}
                ellipsizeMode="tail"
                style={{
                  fontSize: f_s,
                  color: colors.text_grey,
                  fontFamily: regular,
                }}>
                {data.address}
              </Text>
            </View>
          </TouchableOpacity>
        );
      });
    }
  };

  const recent_places_list = () => {
    if (customer_recent_places.length == 0) {
      return (
        <View
          style={{
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text
            numberOfLines={2}
            ellipsizeMode="tail"
            style={{
              fontSize: 12,
              color: colors.text_grey,
              fontFamily: regular,
            }}>
            {t('No recent places searched')}
          </Text>
        </View>
      );
    } else {
      return customer_recent_places.slice(0, 3).map((data, i) => {
        return (
          <TouchableOpacity
            activeOpacity={1}
            key={i}
            onPress={load_location.bind(this, data.lat, data.lng)}
            style={{
              width: '100%',
              flexDirection: 'row',
              borderBottomWidth: 0.5,
              paddingBottom: 10,
              paddingTop: 10,
            }}>
            <View
              style={{
                width: '15%',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Icon
                type={Icons.MaterialIcons}
                name="near-me"
                color={colors.icon_inactive_color}
                style={{fontSize: 22}}
              />
            </View>
            <View
              style={{
                width: '85%',
                alignItems: 'flex-start',
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={2}
                ellipsizeMode="tail"
                style={{
                  fontSize: 16,
                  color: colors.text_grey,
                  fontFamily: regular,
                }}>
                {data.address}
              </Text>
            </View>
          </TouchableOpacity>
        );
      });
    }
  };

  const change_trip_type = async data => {
    ReactNativeHapticFeedback.trigger('impactLight', options);

    setActiveTripType(data.id);
    setTripSubTypes(data.trip_sub_type);
    if (data.trip_sub_type.length > 0) {
      setActiveTripSubType(data.trip_sub_type[0].id);
    } else {
      setActiveTripSubType(0);
    }
  };

  const get_home_api = async () => {
    setLoading(true);
    await axios({
      method: 'post',
      url: api_url + get_home,
      data: {lang: i18n.language, customer_id: global.id},
    })
      .then(async response => {
        setLoading(false);

        if (response.data.status == 1) {
          setTripTypes(response.data.result.trip_types);
          setPackages(response.data.result.packages);
          setCustomerFavourties(response.data.result.customer_favourites);
          setActiveTripType(response.data.result.trip_types[0].id);

        }
      })
      .catch(error => {
        setLoading(false);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const add_favourite_api = async () => {
    fav_RBSheet.current.close();
    console.log('Favourite API');
    console.log(api_url + add_favourite);
    console.log({
      customer_id: global.id,
      address: pickup_address,
      lat: pickup_lat,
      lng: pickup_lng,
    });
    setLoading(true);
    await axios({
      method: 'post',
      url: api_url + add_favourite,
      data: {
        customer_id: global.id,
        address: pickup_address,
        lat: pickup_lat,
        lng: pickup_lng,
      },
    })
      .then(async response => {
        setLoading(false);
        console.log(response.data);
        if (response.data.status == 1) {
          showToast(
            'success',
            t('Success'),
            t('Location added in your favourite spot'),
          );

          setCustomerFavourties(response.data.result);
        }
      })
      .catch(error => {
        setLoading(false);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const view_recent_places = async () => {
    console.log({customer_id: global.id});
    await axios({
      method: 'post',
      url: api_url + get_recent_searches,
      data: {customer_id: global.id},
    })
      .then(async response => {
        setLoading(false);
        setCustomerRecentPlaces(response.data.result);
      })
      .catch(error => {
        setLoading(false);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const get_estimation_fare_api = async (
    lat1,
    lng1,
    lat2,
    lng2,
    package_id,
    sub_type,
    pr,
  ) => {
    console.log("estimation_api")
    console.log(api_url + get_estimation_fare);
    console.log({
      customer_id: global.id,
      pickup_lat: lat1,
      pickup_lng: lng1,
      drop_lat: lat2,
      drop_lng: lng2,
      trip_type: active_trip_type,
      promo: pr,
      lang: i18n.language,
      package_id: package_id,
      days: 1,
      trip_sub_type: sub_type,
    });
    setLoading(true);
    await axios({
      method: 'post',
      url: api_url + get_estimation_fare,
      data: {
        customer_id: global.id,
        pickup_lat: lat1,
        pickup_lng: lng1,
        drop_lat: lat2,
        drop_lng: lng2,
        trip_type: active_trip_type,
        promo: pr,
        lang: i18n.language,
        package_id: package_id,
        days: 1,
        trip_sub_type: sub_type,
      },
    })
      .then(async response => {
        setLoading(false);
        if (response.data.status == 1) {
          setEstimationFares(response.data.result['vehicles']);
          setWallet(response.data.result['wallet']);
          setKm(response.data.result['vehicles'][0].fares.km);
          change_vehicle_type(response.data.result['vehicles'][0].id);
          if (
            pr != 0 &&
            response.data.result['vehicles'][0].fares.discount <= 0
          ) {
            setPromo(0);
            showToast('error', t('Error'), t('Sorry promo not applied'));
          }
        }
      })
      .catch(error => {
        setLoading(false);
      });
  };

  const call_zone = async contact => {
    add_contact_RBSheet.current.close();
    setLoading(true);
    console.log({lat: pickup_lat, lng: pickup_lng});
    await axios({
      method: 'post',
      url: api_url + get_zone,
      data: {lat: pickup_lat, lng: pickup_lng},
    })
      .then(async response => {
        if (response.data.result == 0) {
          setLoading(false);
          showToast(
            'error',
            t('Not Available'),
            t('Our service is not available in your location'),
          );
        } else {
          call_ride_confirm(response.data.result, contact);
        }
      })
      .catch(error => {
        setLoading(false);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const call_ride_confirm = async (zone, contact) => {
    console.log('call_ride_confirm_api');
    console.log(api_url + ride_confirm);
    console.log({
      km: km,
      promo: promo,
      vehicle_type: active_vehicle_type,
      payment_method: 1,
      customer_id: global.id,
      trip_type: active_trip_type,
      surge: 1,
      pickup_address: pickup_address,
      pickup_date: pickup_date,
      pickup_lat: pickup_lat,
      pickup_lng: pickup_lng,
      drop_address: drop_address,
      drop_lat: drop_lat,
      drop_lng: drop_lng,
      package_id: package_id,
      trip_sub_type: active_trip_sub_type,
      stops: JSON.stringify([]),
      zone: zone,
      contact: contact,
    });
    setLoading(true);
    await axios({
      method: 'post',
      url: api_url + ride_confirm,
      data: {
        km: km,
        promo: promo,
        vehicle_type: active_vehicle_type,
        payment_method: 1,
        customer_id: global.id,
        trip_type: active_trip_type,
        surge: 1,
        pickup_address: pickup_address,
        pickup_date: pickup_date,
        pickup_lat: pickup_lat,
        pickup_lng: pickup_lng,
        drop_address: drop_address,
        drop_lat: drop_lat,
        drop_lng: drop_lng,
        package_id: package_id,
        trip_sub_type: active_trip_sub_type,
        stops: JSON.stringify([]),
        zone: zone,
        contact: contact,
      },
    })
      .then(async response => {
        setLoading(false);
        if (response.data.status == 1) {
          setTripRequestId(response.data.result);
          if (response.data.booking_type == 2) {
            showToast(
              'success',
              t('Booking placed successfully'),
              t('You can see you bookings in my rides menu'),
            );
          }
          // booking_exit();
        } else {
          if (
            response.data.message == 'Sorry drivers not available right now'
          ) {
            showToast(
              'error',
              t('No Driver'),
              t('Sorry drivers not available right now'),
            );
          } else {
            showToast('error', t('Error'), response.data.message);
          }
        }
      })
      .catch(error => {
        setLoading(false);
        console.log('error');
        console.log(error);
        showToast('error', t('Error'), t('Sorry something went wrong'));
      });
  };

  const change_vehicle_type = vehicle_type => {
    ReactNativeHapticFeedback.trigger('impactLight', options);
    setActiveVehicleType(vehicle_type);
  };

  

 

  const navigate_promo = () => {
    setModalVisible(true);
  };



 


  const screen_home = () => {
    return (
      <View>
        <Animated.View
          style={[
            {transform: [{translateY: home_comp_1}]},
            [
              {
                position: 'absolute',
                width: '100%',
                height: 60,
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
              },
            ],
          ]}>
          <DropShadow
            style={{
              width: '90%',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0,
              },
              shadowOpacity: 0.3,
              shadowRadius: 5,
            }}>
            <View
              activeOpacity={1}
              style={{
                width: '100%',
                backgroundColor: colors.theme_bg_three,
                borderRadius: 10,
                height: 50,
                flexDirection: 'row',
              }}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  navigation.toggleDrawer();
                  ReactNativeHapticFeedback.trigger('impactLight', options);
                }}
                style={{
                  width: '15%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Icon
                  type={Icons.MaterialIcons}
                  name="menu"
                  color={colors.icon_active_color}
                  style={{fontSize: 22}}
                />
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={1}
                onPress={open_location.bind(this, 1)}
                style={{
                  width: '70%',
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                }}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_xs,
                    fontFamily: normal,
                  }}>
                  {pickup_address}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  fav_RBSheet.current.open();
                  ReactNativeHapticFeedback.trigger('impactLight', options);
                }}
                style={{
                  width: '15%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Icon
                  type={Icons.MaterialIcons}
                  name="favorite-border"
                  color={colors.icon_inactive_color}
                  style={{fontSize: 22}}
                />
              </TouchableOpacity>
            </View>
          </DropShadow>
        </Animated.View>
        <Animated.View
          style={[
            {transform: [{translateY: home_comp_2}]},
            [
              {
                position: 'absolute',
                bottom: 0,
                width: '100%',
                height: 100,
                // backgroundColor: colors.theme_bg_three,
              },
            ],
          ]}>       
          <View style={{margin: 5}} />
  
            <View>
              <DropShadow
                style={{
                  width: '100%',
                  padding: 10,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                  shadowOpacity: 0.3,
                  shadowRadius: 5,
                }}>
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={open_location.bind(this, 2)}
                  style={{
                    width: '100%',
                    backgroundColor: colors.theme_bg_three,
                    borderRadius: 10,
                    height: 50,
                    flexDirection: 'row',
                  }}>
                  <View
                    style={{
                      width: '15%',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Icon
                      type={Icons.MaterialIcons}
                      name="search"
                      color={colors.theme_fg_two}
                      style={{fontSize: 30}}
                    />
                  </View>
                  <View
                    style={{
                      width: '85%',
                      alignItems: 'flex-start',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: colors.theme_fg_two,
                        fontSize: 16,
                        color: colors.text_grey,
                      }}>
                      {t('Where are u going')}?
                    </Text>
                  </View>
                </TouchableOpacity>
              </DropShadow>
            </View>
        
        </Animated.View>
      </View>
    );
  };

  const screen_location = () => {
    return (
      <View>
        <Animated.View
          style={[
            {transform: [{translateY: drop_comp_3}]},
            [
              {
                position: 'absolute',
                width: '100%',
                alignItems: 'flex-start',
                padding: 10,
                paddingBottom: 10,
                justifyContent: 'center',
                zIndex: 10,
              },
              {marginTop: isNotchDevice ? insets.top : 0},
            ],
          ]}>
          <DropShadow
            style={{
              width: '100%',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0,
              },
              shadowOpacity: 0.3,
              shadowRadius: 5,
            }}>
            
          </DropShadow>
        </Animated.View>
        <Animated.View
          style={[
            {transform: [{translateY: drop_comp_2}]},
            [
              {
                position: 'absolute',
                bottom: 10,
                width: '100%',
                height: 100,
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
              },
            ],
          ]}>
          <DropShadow
            style={{
              width: '100%',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0,
              },
              shadowOpacity: 0.3,
              shadowRadius: 5,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={confirm_location.bind(this)}
              style={{
                width: '90%',
                backgroundColor: colors.btn_color,
                borderRadius: 10,
                height: 50,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                alignSelf: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  color: colors.theme_fg_three,
                  fontFamily: bold,
                }}>
                {t('Confirm Location')}
              </Text>
            </TouchableOpacity>
          </DropShadow>
        </Animated.View>
        <Animated.View
          style={[
            {transform: [{translateY: drop_comp_4}]},
            [
              {
                position: 'absolute',
                width: '100%',
                height: screenHeight,
                alignItems: 'center',
                paddingBottom: 10,
                justifyContent: 'flex-start',
                backgroundColor: colors.theme_bg_three,
                zIndex: 10,
              },
            ],
          ]}>
          <View style={{marginTop: isNotchDevice ? 150 + insets.top : 150}} />
          <TouchableOpacity
            activeOpacity={1}
            onPress={search_exit.bind(this)}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              padding: 10,
              borderRadius: 10,
              borderColor: colors.grey,
            }}>
            <Icon
              type={Icons.MaterialIcons}
              name="location-on"
              color={colors.icon_inactive_color}
              style={{fontSize: 22}}
            />
            <View style={{margin: 5}} />
            <Text
              style={{fontSize: 18, color: colors.text_grey, fontFamily: bold}}>
              {t('Locate on map')}
            </Text>
          </TouchableOpacity>
          <View style={{margin: 10}} />
          <ScrollView>
            <Text
              style={{
                fontSize: 18,
                color: colors.text_grey,
                fontFamily: bold,
                marginLeft: 10,
              }}>
              {t('Recent Places')}
            </Text>
            <ScrollView style={{width: '100%', padding: 10}}>
              {recent_places_list()}
              <View style={{margin: 10}} />
            </ScrollView>
            <Text
              style={{
                fontSize: 18,
                color: colors.text_grey,
                fontFamily: bold,
                marginLeft: 10,
              }}>
              {t('Favourite Locations')}
            </Text>
            <ScrollView style={{width: '100%'}}>
              {favourites_list()}
              <View style={{margin: 10}} />
            </ScrollView>
          </ScrollView>
        </Animated.View>
        <Animated.View
          style={[
            {transform: [{translateY: drop_comp_1}]},
            [
              {
                position: 'absolute',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
              },
            ],
          ]}>
          <DropShadow
            style={{
              width: '90%',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0,
              },
              shadowOpacity: 0.3,
              shadowRadius: 5,
            }}>
            <View
              style={[
                {
                  flexDirection: 'row',
                  width: '100%',
                  backgroundColor: colors.theme_bg_three,
                  borderRadius: 10,
                },
                {marginTop: isNotchDevice ? insets.top : 0},
              ]}>
               <GooglePlacesAutocomplete
                ref={search}
                minLength={2}
                placeholder={
                  active_location == 1
                    ? t('Enter The Pickup Location')
                    : t('Enter The Destination')
                }
                listViewDisplayed="auto"
                predefinedPlaces={[]}
                predefinedPlacesAlwaysVisible={false}
                fetchDetails={true} // Ensure this is true to get details
                GooglePlacesSearchQuery={{
                  rankby: 'distance',
                  types: 'food',
                }}
                debounce={200}
                filterReverseGeocodingByTypes={
                  Array.isArray(['locality', 'administrative_area_level_3'])
                    ? ['locality', 'administrative_area_level_3']
                    : []
                } // ✅ Ensures it's always an array
                textInputProps={{
                  onFocus: () => is_focus(),
                  placeholderTextColor: colors.text_grey,
                  returnKeyType: 'search',
                }}
                styles={{
                  textInputContainer: {
                    backgroundColor: colors.theme_bg_three,
                    borderRadius: 10,
                    marginLeft: 10,
                    marginTop: 5,
                  },
                  description: {
                    color: '#000',
                  },
                  textInput: {
                    height: 40,
                    color: colors.theme_fg_two,
                    fontFamily: normal,
                    fontSize: f_xs,
                    borderTopLeftRadius: 10,
                    borderTopRightRadius: 10,
                    backgroundColor: colors.theme_bg_three,
                  },
                  predefinedPlacesDescription: {
                    color: colors.theme_fg_two,
                  },
                }}
                onFail={error => {
                  console.log('Google API Error:', error);
                }}
                currentLocation={false}
                enableHighAccuracyLocation={true}
                onPress={(data, details = null) => {
                  console.log('Selected Place Data:', data);
                  console.log('Selected Place Details:', details);

                  if (!details) {
                    console.log(
                      '⚠️ No details returned from API. Possible API issue.',
                    );
                    return;
                  }

                  get_location(data, details);
                }}
                query={{
                  key: GOOGLE_KEY,
                  language: 'en',
                  radius: '1500',
                  location: `${pickup_lat},${pickup_lng}`,
                  types: 'geocode', // ✅ Fix: Single string instead of an array
                }}
              /> 

             
            </View>
          </DropShadow>
        </Animated.View>
      </View>
    );
  };

  const screen_booking = () => {
    return (
      <View>
        {!current_location_status && (
          <DropShadow
            style={{
              width: '100%',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 5,
              },
              shadowOpacity: 0.3,
              shadowRadius: 5,
            }}>
            <TouchableOpacity
              activeOpacity={0}
              onPress={booking_exit.bind(this)}
              style={[
                {
                  width: 40,
                  height: 40,
                  backgroundColor: colors.theme_bg_three,
                  borderRadius: 25,
                  alignItems: 'center',
                  justifyContent: 'center',
                  top: 20,
                  left: 20,
                  elevation: 5,
                },
                {marginTop: isNotchDevice ? insets.top : 0},
              ]}>
              <Icon
                type={Icons.MaterialIcons}
                name="arrow-back"
                color={colors.icon_active_color}
                style={{fontSize: 22, color: colors.theme_bg_two}}
              />
            </TouchableOpacity>
          </DropShadow>
        )}
        <Animated.View
          style={[
            {transform: [{translateY: book_comp_1}]},
            [
              {
                position: 'absolute',
                width: '100%',
                height: screenHeight - 250,
                paddingBottom: 10,
                justifyContent: 'flex-start',
                backgroundColor: colors.theme_bg_three,
                borderTopStartRadius: 20,
                borderTopEndRadius: 20,
              },
            ],
          ]}>
          <View style={{width: '100%', height: 130, paddingHorizontal: 0}}>
            <DropShadow
              style={{
                width: '100%',
                shadowColor: '#ccc',
                shadowOffset: {width: 0, height: 8},
                shadowOpacity: 0.15,
                shadowRadius: 15,
                borderRadius: 16,
              }}>
              <View
                style={{
                  width: '100%',
                  backgroundColor: 'white',
                  borderTopStartRadius: 20,
                  borderTopEndRadius: 20,
                  overflow: 'hidden',
                  borderWidth: 1,
                  borderColor: '#F0F0F5',
                }}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={{
                    width: '100%',
                    paddingVertical: 16,
                    paddingHorizontal: 18,
                    backgroundColor: 'white',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        width: 28,
                        height: 28,
                        borderRadius: 14,
                        backgroundColor: '#4CD96420',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: 14,
                      }}>
                      <View
                        style={{
                          width: 10,
                          height: 10,
                          borderRadius: 5,
                          backgroundColor: '#4CD964',
                        }}
                      />
                    </View>
                    <Text
                      numberOfLines={1}
                      ellipsizeMode="tail"
                      style={{
                        flex: 1,
                        color: '#2D2D3A',
                        fontSize: f_xs,
                        fontFamily: regular,
                        letterSpacing: -0.2,
                      }}>
                      {pickup_address}
                    </Text>
                  </View>
                </TouchableOpacity>

                <View
                  style={{
                    height: 1,
                    width: '100%',
                    backgroundColor: '#F0F0F5',
                    marginLeft: 58,
                  }}
                />

                <TouchableOpacity
                  activeOpacity={0.8}
                  style={{
                    width: '100%',
                    paddingVertical: 16,
                    paddingHorizontal: 18,
                    backgroundColor: 'white',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        width: 28,
                        height: 28,
                        borderRadius: 14,
                        backgroundColor: '#FF3B3020',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: 14,
                      }}>
                      <View
                        style={{
                          width: 10,
                          height: 10,
                          borderRadius: 5,
                          backgroundColor: '#FF3B30',
                        }}
                      />
                    </View>
                    <Text
                      numberOfLines={1}
                      ellipsizeMode="tail"
                      style={{
                        flex: 1,
                        color: '#2D2D3A',
                        fontSize: f_xs,
                        fontFamily: regular,
                        letterSpacing: -0.2,
                      }}>
                      {drop_address}
                    </Text>
                  </View>
                </TouchableOpacity>

              </View>
            </DropShadow>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>           
            <View style={{paddingHorizontal: 10}}>
              <View style={{margin: 5}} />
              {estimation_fare_list()}
            </View>
          </ScrollView>
          <View
            style={{
              height: 135,
              alignItems: 'center',
              justifyContent: 'flex-end',
              marginBottom: 10,
            }}>
            <View
              style={{
                width: '100%',
                height: 30,
                backgroundColor: colors.theme_bg,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={{
                  color: colors.theme_fg_three,
                  fontSize: 12,
                  fontFamily: normal,
                  letterSpacing: 1,
                }}>
                {t('You have')} {global.currency}
                {wallet} {t('in your wallet')} !
              </Text>
            </View>
            <View style={{height: 40, width: '100%', flexDirection: 'row'}}>
              <TouchableOpacity
                style={{
                  width: '46%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'row',
                }}>
                <Image source={money_icon} style={{width: 30, height: 40}} />
                <View style={{margin: 5}} />
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: 16,
                    fontFamily: regular,
                  }}>
                  {t('Cash')}
                </Text>
              </TouchableOpacity>
              <View
                style={{
                  margin: '2%',
                  borderLeftWidth: 1,
                  borderColor: colors.grey,
                }}
              />
              <TouchableOpacity
                activeOpacity={1}
                onPress={navigate_promo.bind(this)}
                style={{
                  width: '49%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'row',
                }}>
                <Text
                  numberOfLines={1}
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: 16,
                    fontFamily: regular,
                  }}>
                  {t('Coupons')}
                </Text>
                <View style={{margin: 5}} />
                <Image source={discount_icon} style={{width: 30, height: 30}} />
              </TouchableOpacity>
            </View>
            {loading == false ? (
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 50,
                }}>
                <TouchableOpacity
                  onPress={call_zone.bind(this, 'null')}
                  activeOpacity={1}
                  style={{
                    width: '90%',
                    alignSelf:'center',
                    backgroundColor: colors.btn_color,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height:40
                  }}>
                  <Text
                    style={{
                      color: colors.theme_fg_two,
                      fontSize: f_s,
                      color: colors.theme_fg_three,
                      fontFamily: regular,
                      padding: 10,
                    }}>
                    {t('Book Self')}
                  </Text>
                </TouchableOpacity>
            
              </View>
            ) : (
              <View style={{height: 50, width: '90%', alignSelf: 'center'}}>
                <LottieView
                  style={{flex: 1}}
                  source={btn_loader}
                  autoPlay
                  loop
                />
              </View>
            )}
          </View>
        </Animated.View>
      </View>
    );
  };

  const rb_favourite = () => {
    return (
      <RBSheet
        ref={fav_RBSheet}
        height={170}
        openDuration={250}
        customStyles={{
          container: {
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            padding: 10,
          },
        }}>
        <View style={{padding: 10, width: '100%'}}>
          <Text
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xl,
              fontFamily: normal,
            }}>
            {t('Save as favourite')}
          </Text>
          <View style={{margin: 5}} />
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xs,
              fontFamily: regular,
            }}>
            {pickup_address}
          </Text>
        </View>
        <View style={{margin: 10}} />
        <View style={{flexDirection: 'row', width: '100%'}}>
          <View style={{width: '1%'}} />
          <View
            style={{
              width: '48%',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => fav_RBSheet.current.close()}
              style={{
                width: '100%',
                backgroundColor: colors.lite_grey,
                borderRadius: 5,
                height: 50,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  color: colors.theme_fg_two,
                  fontFamily: normal,
                }}>
                {t('Cancel')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{width: '1%'}} />
          <View
            style={{
              width: '48%',
              alignItems: 'center',
              justifyContent: 'center',
              alignSelf: 'flex-end',
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => add_favourite_api()}
              style={{
                width: '100%',
                backgroundColor: colors.btn_color,
                borderRadius: 5,
                height: 50,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  color: colors.theme_fg_three,
                  fontFamily: normal,
                }}>
                {t('Save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    );
  };

  const rb_add_contact = () => {
    return (
      <RBSheet
        ref={add_contact_RBSheet}
        height={250}
        openDuration={250}
        customStyles={{
          container: {
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            padding: 10,
          },
        }}>
        <View style={{padding: 10, width: '100%'}}>
          <Text
            style={{
              color: colors.theme_fg_two,
              fontSize: 25,
              fontFamily: normal,
            }}>
            {t('Someone else taking this ride')} ?
          </Text>
          <View style={{margin: 5}} />
          <TextInput
            ref={inputRef}
            secureTextEntry={false}
            keyboardType="numeric"
            placeholder={t('Enter Contact Number')}
            placeholderTextColor={colors.grey}
            style={styles.textinput}
            onChangeText={TextInputValue => setContactNumber(TextInputValue)}
          />
        </View>
        <View style={{margin: 10}} />
        <View style={{flexDirection: 'row', width: '100%'}}>
          <View style={{width: '1%'}} />
          <View
            style={{
              width: '48%',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => add_contact_RBSheet.current.close()}
              style={{
                width: '100%',
                backgroundColor: colors.lite_grey,
                borderRadius: 5,
                height: 50,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  color: colors.theme_fg_two,
                  fontFamily: regular,
                }}>
                {t('Cancel')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{width: '1%'}} />
          <View
            style={{
              width: '48%',
              alignItems: 'center',
              justifyContent: 'center',
              alignSelf: 'flex-end',
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={call_contact_number_validation.bind()}
              style={{
                width: '100%',
                backgroundColor: colors.btn_color,
                borderRadius: 5,
                height: 50,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 16,
                  color: colors.theme_fg_three,
                  fontFamily: regular,
                }}>
                {t('Book Now')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    );
  };

  const call_contact_number_validation = () => {
    if (contact_number == '') {
      alert(t('Enter phone number to contact'));
    } else {
      call_zone(contact_number);
    }
  };

  const cancel_request = () => {
    setLoading(true);
    console.log(api_url + trip_request_cancel);
    console.log({trip_request_id: trip_request_id});
    axios({
      method: 'post',
      url: api_url + trip_request_cancel,
      data: {trip_request_id: trip_request_id},
    })
      .then(async response => {
        setLoading(false);
      })
      .catch(error => {
        setLoading(false);
        console.log(error);
      });
  };

  const search_dialog = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={search_status==1}
        statusBarTranslucent={true}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}>
          <View
            style={{
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            {/* Animated Background */}
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
            
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <LottieView
                style={{width: '100%', height: '100%'}}
                source={search}
                autoPlay
                loop
                speed={0.5}
              />
            </View>

            <View
              style={{
                backgroundColor: colors.theme_bg_three,
                width: '100%',
                maxWidth: 400,
                borderRadius: 20,
                padding: 30,
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 10},
                shadowOpacity: 0.3,
                shadowRadius: 20,
                elevation: 20,
                transform: [{scale: 0.95}],
              }}>
              <View
                style={{
                  width: 200,
                  height: 200,
                  marginBottom: 20,
                }}>
                <LottieView
                  style={{flex: 1}}
                  source={search_loader}
                  autoPlay
                  loop
                  speed={1.2}
                />
              </View>

           

              <Text
                style={{
                  fontSize: 22,
                  fontFamily: normal,
                  color: colors.theme_fg_two,
                  textAlign: 'center',
                  marginBottom: 10,
                  lineHeight: 30,
                }}>
                {t('Searching for nearby drivers')}...
              </Text>

              <Text
                style={{
                  fontSize: 16,
                  fontFamily: regular,
                  color: colors.text_grey,
                  textAlign: 'center',
                  marginBottom: 30,
                }}>
                {t('We are finding the best available driver for you')}
              </Text>

              {loading == false ? (
                <TouchableOpacity
                  onPress={cancel_request}
                  activeOpacity={0.8}
                  style={{
                    backgroundColor: colors.error,
                    paddingVertical: 15,
                    paddingHorizontal: 40,
                    borderRadius: 50,
                    flexDirection: 'row',
                    alignItems: 'center',
                    shadowColor: colors.error,
                    shadowOffset: {width: 0, height: 5},
                    shadowOpacity: 0.3,
                    shadowRadius: 10,
                    elevation: 5,
                  }}>
                  <Icon
                    type={Icons.MaterialIcons}
                    name="close"
                    color="#fff"
                    style={{fontSize: 20, marginRight: 10}}
                  />
                  <Text
                    style={{
                      color: '#fff',
                      fontSize: 18,
                      fontFamily: bold,
                    }}>
                    {t('Cancel Search')}
                  </Text>
                </TouchableOpacity>
              ) : (
                <View
                  style={{
                    width: 150,
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <LottieView
                    style={{width: 100, height: 100}}
                    source={btn_loader}
                    autoPlay
                    loop
                  />
                </View>
              )}
          
            </View>

          
          </View>
        </View>
      </Modal>
    );
  };

 

  const modal = () => {
    return (
      <View>
        <Modal
          isVisible={is_modal_visible}
     
          style={{
            width: '90%',
            height: '60%',
            backgroundColor: colors.theme_bg_three,
            borderRadius: 10,
          }}>
          <View style={{width: '100%', flexDirection: 'row', padding: 20}}>
            <View
              style={{
                width: '80%',
                alignItems: 'flex-start',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: colors.theme_fg_two,
                  fontSize: 20,
                  fontFamily: regular,
                }}>
                {t('Promo Codes')}
              </Text>
            </View>
            <TouchableOpacity
              onPress={toggleModal.bind(this)}
              style={{
                width: '20%',
                alignItems: 'flex-end',
                justifyContent: 'center',
              }}>
              <Icon
                type={Icons.MaterialIcons}
                name="close"
                color={colors.icon_inactive_color}
                style={{fontSize: 30}}
              />
            </TouchableOpacity>
          </View>
          <FlatList
            data={promo_list}
            renderItem={show_promo_list}
            keyExtractor={item => item.id}
          />
        </Modal>
      </View>
    );
  };

  const show_promo_list = ({item}) => (
    <View style={{alignItems: 'center', borderBottomWidth: 0.5}}>
      <View
        style={{
          width: '100%',
          backgroundColor: colors.theme_bg_three,
          borderRadius: 10,
          padding: 20,
          marginTop: 5,
          marginBottom: 5,
        }}>
        <View
          style={{
            width: '100%',
            alignItems: 'flex-start',
            justifyContent: 'center',
          }}>
          <Text
            style={{
              color: colors.theme_fg_two,
              fontSize: 16,
              fontFamily: normal,
            }}>
            {item.promo_name}
          </Text>
          <View style={{margin: 3}} />
          <Text
            style={{
              color: colors.theme_fg_two,
              fontSize: 14,
              fontFamily: regular,
            }}>
            {item.description}
          </Text>
        </View>
        <View style={{margin: 5}} />
        <View
          style={{
            width: '100%',
            borderRadius: 10,
            flexDirection: 'row',
            borderWidth: 1,
            padding: 10,
            backgroundColor: colors.text_container_bg,
            borderStyle: 'dotted',
          }}>
          <View
            style={{
              width: '70%',
              alignItems: 'flex-start',
              justifyContent: 'center',
            }}>
            {item.promo_type == 5 && (
              <Text
                style={{
                  color: colors.theme_fg,
                  fontSize: 16,
                  fontFamily: normal,
                }}>
                {item.discount}
                {global.currency} {t('OFF')}
              </Text>
            )}
            {item.promo_type == 6 && (
              <Text
                style={{
                  color: colors.theme_fg,
                  fontSize: 16,
                  fontFamily: normal,
                }}>
                {item.discount}% {t('OFF')}
              </Text>
            )}
          </View>
          {loading == true ? (
            <View style={{height: 50, width: '90%', alignSelf: 'center'}}>
              <LottieView style={{flex: 1}} source={btn_loader} autoPlay loop />
            </View>
          ) : (
            <TouchableOpacity
              onPress={call_apply_promo.bind(this, item)}
              activeOpacity={1}
              style={{
                width: '30%',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: colors.theme_bg,
                borderRadius: 10,
                padding: 10,
              }}>
              <Text
                style={{
                  color: colors.theme_fg_three,
                  fontSize: 14,
                  fontFamily: normal,
                }}>
                {t('Apply')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container]}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <View style={{zIndex: 100}}>
        <Toast />
      </View>
  

      <MapView
        provider={PROVIDER_GOOGLE}
        ref={map_ref}
        style={styles.map}
        initialRegion={region}
        showsUserLocation={true}
        showsMyLocationButton={false}
        toolbarEnabled={false}
        onMapReady={() => setOnLoaded(1)}
        onRegionChangeComplete={handle_region_change_complete}></MapView>

      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 120,
          right: 20,
          backgroundColor: '#fff',
          borderRadius: 50,
          padding: 10,
          elevation: 5,
        }}
        onPress={centerToUserLocation}>
        <Icon
          type={Icons.MaterialIcons}
          name="my-location"
          color={colors.theme_bg_two}
          style={{fontSize: 22}}
        />
      </TouchableOpacity>
      <View
        style={{
          height: 100,
          width: 100,
          alignSelf: 'center',
          position: 'absolute',
          top: screenHeight / 2 - 50,
        }}>
        <LottieView style={{flex: 1}} source={pin_marker} autoPlay loop />
      </View>
      {screen_home()}
      {screen_location()}
      {screen_booking()}
      {rb_favourite()}
      {rb_add_contact()}

      {search_dialog()}
      {modal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 70,
    backgroundColor: colors.lite_bg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  container: {
    height: screenHeight,
    width: screenWidth,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  vehicle_img: {
    height: 40,
    width: 55,
  },
  active_vehicle_img: {
    height: 50,
    width: 80,
  },
  active_trip_type_label: {
    color: colors.theme_fg_two,
    fontSize: f_tiny,
    fontFamily: bold,
  },
  inactive_trip_type_label: {
    color: colors.text_grey,
    fontSize: f_tiny,
    fontFamily: normal,
  },
  trip_type_container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  segment_active_bg: {
    width: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    marginLeft: '1%',
    marginRight: '1%',
    backgroundColor: colors.theme_bg,
    borderRadius: 10,
  },
  segment_active_fg: {
    color: colors.theme_fg_two,
    fontSize: 14,
    fontFamily: bold,
    color: colors.theme_fg_three,
  },
  segment_inactive_bg: {
    width: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    marginLeft: '1%',
    marginRight: '1%',
    backgroundColor: colors.lite_bg,
    borderRadius: 10,
  },
  segment_inactive_fg: {
    color: colors.theme_fg_two,
    fontSize: 14,
    fontFamily: normal,
    color: colors.theme_fg_two,
  },
  textinput: {
    fontSize: f_m,
    color: colors.grey,
    fontFamily: regular,
    height: 60,
    backgroundColor: colors.text_container_bg,
    width: '100%',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '98%',
    backgroundColor: colors.warning_background,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },

  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: 'red',
  },
  modalText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: 'grey',
    color: colors.theme_bg_two,
  },
});

function mapStateToProps(state) {
  return {
    initial_lat: state.booking.initial_lat,
    initial_lng: state.booking.initial_lng,
    initial_region: state.booking.initial_region,
  };
}

export default connect(mapStateToProps)(Dashboard);
