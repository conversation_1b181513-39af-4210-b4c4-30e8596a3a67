import { useTranslation } from "react-i18next";
import '../languages/i18next';

import SegmentedControl from '@react-native-segmented-control/segmented-control';

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,

    ScrollView,
    Image,
    StatusBar,
    FlatList
} from "react-native";
import { useNavigation, CommonActions } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import Icon, { Icons } from '../components/Icons';
import { normal, bold, trip_details, my_bookings, api_url, img_url, loader, no_data_loader, cancel, f_s, f_xs, f_tiny, f_xl, regular, logo } from '../config/Constants';
import DropShadow from "react-native-drop-shadow";
import { Badge } from 'react-native-paper';
import axios from 'axios';
import moment from 'moment';
import Moment from 'moment';
import LottieView from 'lottie-react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
  useSharedValue,
  useDerivedValue,
} from 'react-native-reanimated';
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import 'moment/locale/ar';

import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const MyRides = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState([]);
    const [filter, setFilter] = useState(1);
    const viewableItems = useSharedValue([]);
    const [cancellation_statuses, setCancellationStatuses] = useState([6, 7]);

    const go_back = () => {
        ReactNativeHapticFeedback.trigger("impactLight", options);
        navigation.toggleDrawer();
    }


    useEffect(() => {
        if (i18n.language === 'ar') {
              moment.locale('ar');
            } else {
              moment.locale('en');
            }
        call_my_bookings(1);
    }, []);

    const change_filter = (id) => {
ReactNativeHapticFeedback.trigger("impactLight", options);

        setFilter(id);
        call_my_bookings(id);
    }

    const call_my_bookings = (fl) => {
        setLoading(true);
        axios({
            method: 'post',
            url: api_url + my_bookings,
            data: { customer_id: global.id, lang: i18n.language, filter: fl }
        })
            .then(async response => {
                setTimeout(function () {
                    setLoading(false);
                    setData(response.data.result)
                   // console.log(response.data.result)
                }, 1000)
                
            })
            .catch(error => {
                setLoading(false);
                    showToast(
                      "error",
                      t('Error'),
                      t('Sorry something went wrong')
                    );
            });
    }
    const showToast = (type, title, message) => {
    ReactNativeHapticFeedback.trigger("impactLight", options);

        Toast.show({
        type: type,
        text1: title,
        text2: message,
        visibilityTime: 5000,
        position: 'top',
        topOffset: 200,
        });
    };
    const navigate = (trip_id, filter, status) => {
        if (filter == 1 && status == 5) {
            navigation.navigate('Bill', { trip_id: trip_id, from: 'trips' })
        }else if (filter == 2) {
            call_trip_details(trip_id);
        } else if (filter == 3) {
            navigation.navigate('Bill', { trip_id: trip_id, from: 'trips' })
        }
    }

    const call_trip_details = (trip_id) => {
        axios({
            method: 'post',
            url: api_url + trip_details,
            data: { trip_id: trip_id }
        })
            .then(async response => {
                navigation.navigate('TripDetails', { trip_id: trip_id, from: 'trips', data: response.data.result })
            })
            .catch(error => {
                console.log(error)
            });
    }

    const navigate_home = () => {
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: "Home" }],
            })
        );
    }

  const ListItem = React.memo(
    ({item, viewableItems}) => {
      const isItemVisible = useDerivedValue(() => {
        const visible = viewableItems.value.find(
          v => v?.item?.id === item.id && v.isViewable,
        );
        return !!visible;
      }, [viewableItems]);

      const rStyle = useAnimatedStyle(() => ({
        opacity: withTiming(isItemVisible.value ? 1 : 1, {duration: 200}),
        transform: [
          {
            scale: withTiming(isItemVisible.value ? 1 : 0.95, {duration: 200}),
          },
        ],
      }));
      const handlePress = useCallback(() => {
        navigate(item.id, filter, item.status);
    }, [item.id, filter, item.status, navigate]);
      return (
        <Animated.View style={[{width: '100%'}, rStyle]}>
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={handlePress}
          style={{alignItems: 'center', borderRadius: 10, padding: 10}}>
          <DropShadow
            style={{
              width: '95%',
              marginVertical: 5,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: 0.1,
              shadowRadius: 5,
            }}>
            {/* Driver Info Row */}
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: colors.theme_bg_three,
                padding: 15,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
              }}>
              {/* Driver Avatar */}
              <View style={{width: '17%', justifyContent: 'center'}}>
                <Image
                  style={{width: 50, height: 50, borderRadius: 10}}
                  source={{
                    uri: img_url + item.profile_picture,
                    cache: 'force-cache',
                  }}
                  resizeMode="cover"
                />
              </View>

              {/* Driver Name and Rating */}
              <View style={{width: '33%', justifyContent: 'center'}}>
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_s,
                    fontFamily: bold,
                  }}>
                  {item.driver_name}
                </Text>
                <View style={{margin: 2}} />
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Icon name="star" size={20} color={colors.warning} />
                  <Text
                    style={{
                      color: colors.theme_bg_two,
                      fontSize: f_s,
                      fontFamily: bold,
                      marginLeft: 3,
                    }}>
                    {item.ratings}
                  </Text>
                </View>
              </View>

              {/* Fare */}
              <View
                style={{
                  width: '25%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: colors.text_grey,
                    fontSize: f_xs,
                    fontFamily: normal,
                  }}>
                  {t('Fare')}
                </Text>
                <Text
                  style={{
                    fontSize: f_s,
                    fontFamily: bold,
                    color: colors.theme_fg_two,
                    marginTop: 3,
                  }}>
                  {global.currency}
                  {item.total}
                </Text>
              </View>

              {/* Distance */}
              <View
                style={{
                  width: '25%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: colors.text_grey,
                    fontSize: f_xs,
                    fontFamily: normal,
                  }}>
                  {t('Distance')}
                </Text>
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_s,
                    fontFamily: bold,
                    marginTop: 3,
                  }}>
                  {item.distance} {t('km')}
                </Text>
              </View>
            </View>

            {/* Divider */}
            <View
              style={{
                borderBottomWidth: 0.5,
                borderColor: colors.grey,
              }}
            />

            {/* Address Section */}
            <View
              style={{
                backgroundColor: colors.theme_bg_three,
                padding: 15,
                borderBottomLeftRadius: 10,
                borderBottomRightRadius: 10,
              }}>
              <View style={{width: '90%'}}>
                {/* Pickup Address */}
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View style={{ width: 20,height: 20,borderRadius: 12,backgroundColor: 'rgba(76, 217, 100, 0.2)',alignItems: 'center',justifyContent: 'center',marginRight: 0,}}>
                    <View
                        style={{
                        width: 10,
                        height: 10,
                        borderRadius: 5,
                        backgroundColor: '#4CD964',
                        }}
                    />
                    </View>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    style={{
                      color: colors.theme_fg_two,
                      fontSize: f_xs,
                      fontFamily: normal,
                      marginLeft: 5,
                    }}>
                    {item.actual_pickup_address || item.pickup_address}
                  </Text>
                </View>

                {/* Drop Address */}
                {item.trip_type !== 'Rental' && (
                  <>
                    <View
                      style={{
                        height: 20,
          
                        marginLeft: 3,
                    
                      }}
                    />
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 5,
                      }}>
                       <View
                            style={{
                            width: 20,
                            height: 20,
                            borderRadius: 12,
                            backgroundColor: 'rgba(255, 59, 48, 0.2)',
                            alignItems: 'center',
                            justifyContent: 'center',
                          
                        }}>
                            <View
                                style={{
                                width: 10,
                                height: 10,
                                borderRadius: 5,
                                backgroundColor: '#FF3B30',
                                }}
                            />
                        </View>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={{
                          color: colors.theme_fg_two,
                          fontSize: f_xs,
                          fontFamily: normal,
                          marginLeft: 5,
                        }}>
                        {item.actual_drop_address || item.drop_address}
                      </Text>
                    </View>
                  </>
                )}
              </View>

              {/* Date and Time */}
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 10,
                }}>
                <Text
                  style={{
                    fontSize: f_tiny,
                    fontFamily: normal,
                    color: colors.text_grey,
                  }}>
                  {Moment(item.pickup_date).format('DD-MMM-YYYY')}
                </Text>
                <Text
                  style={{
                    fontSize: f_tiny,
                    fontFamily: normal,
                    color: colors.text_grey,
                  }}>
                  {Moment(item.pickup_date).format('hh:mm a')}
                </Text>
              </View>
            </View>
          </DropShadow>

          {/* Cancellation Overlay */}
          {cancellation_statuses.includes(parseInt(item.status)) && (
            <View
              style={{
                position: 'absolute',
                top: 0,
                right: 0,
                left: 0,
                bottom: 0,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
              }}>
              <Image
                style={{width: 100, height: 100}}
                source={cancel}
                resizeMode="contain"
              />
            </View>
          )}
        </TouchableOpacity>
        </Animated.View>
      );
    },
    (prev, next) => prev.item.id === next.item.id,
  ); // memo check

  const viewabilityConfigCallbackPairs = useRef([
    {
      viewabilityConfig: {
        itemVisiblePercentThreshold: 40,
        minimumViewTime: 150,
      },
      onViewableItemsChanged: ({viewableItems: vItems}) => {
        viewableItems.value = vItems;
      },
    },
  ]);


  const keyExtractor = useCallback(item => item.id.toString(), []);

  return (
    <SafeAreaView style={{backgroundColor: colors.lite_bg, flex: 1}}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />

      <View style={[styles.header]}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={go_back.bind(this)}
          style={{
            width: '15%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Icon
            type={Icons.MaterialIcons}
            name="arrow-back"
            color={colors.theme_fg_two}
            style={{fontSize: 30}}
          />
        </TouchableOpacity>
        <View
          activeOpacity={1}
          style={{
            width: '85%',
            alignItems: 'flex-start',
            justifyContent: 'center',
          }}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xl,
              fontFamily: regular,
            }}>
            {t('My Rides')}
          </Text>
        </View>
      </View>
      <View style={{alignItems: 'center', margin: 10}}>
        <DropShadow
          style={{
            width: '95%',
            shadowColor: colors.text_grey,
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.2,
            shadowRadius: 5,
            elevation: 5,
          }}>
          <View
            style={{
              backgroundColor: colors.theme_bg_three,
              borderRadius: 10,
          

              overflow: 'hidden',
            }}>
            <SegmentedControl
              values={[t('All'), t('Upcoming'), t('Completed')]}
              selectedIndex={filter - 1}
              onChange={event => {
                const selectedIndex = event.nativeEvent.selectedSegmentIndex;
                change_filter(selectedIndex + 1);
              }}
              tintColor={colors.btn_color}
              fontStyle={{
                fontFamily: regular,
                fontSize: 14,
                letterSpacing: 0.5,
                color: colors.text_grey,
              }}
              activeFontStyle={{
                fontFamily: regular,
                color: colors.theme_bg_three,
                fontSize: 14,
                letterSpacing: 0.5,
              }}
              backgroundColor="transparent"
              style={{
                height: 48,
                borderWidth: 0,
              }}
              activeTabStyle={{
                backgroundColor: colors.btn_color,
                borderRadius: 12,
                shadowColor: colors.btn_color,
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 4,
              }}
              tabsContainerStyle={{
                padding: 4,
                justifyContent: 'space-between',
              }}
              tabStyle={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 8,
                marginHorizontal: 2,
              }}
            />

            {/* Optional animated underline */}
            {Platform.OS === 'ios' && (
              <Animated.View
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  height: 3,
                  width: `${100 / 3}%`,
                  backgroundColor: colors.btn_color,
                  transform: [
                    {
                      translateX: new Animated.Value((filter - 1) * (100 / 3)),
                    },
                  ],
                }}
              />
            )}
          </View>
        </DropShadow>
      </View>
      {loading == true ? (
        <View
          style={{
            height: 100,
            width: 100,
            alignSelf: 'center',
            marginTop: '30%',
          }}>
          <LottieView style={{flex: 1}} source={loader} autoPlay loop />
        </View>
      ) : (
        <View>
          {data.length > 0 ? (
            <FlatList
              data={data}
              showsVerticalScrollIndicator={false}
              keyExtractor={keyExtractor}
              renderItem={({item}) => (
                <ListItem item={item} viewableItems={viewableItems} />
              )}
              viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
              initialNumToRender={8}
              maxToRenderPerBatch={5}
              windowSize={7}
              removeClippedSubviews={true}
              updateCellsBatchingPeriod={50}
              ListFooterComponent={<View style={{margin:'30%'}} />}
            />
          ) : (
            <View
              style={{
                height: 300,
                width: 300,
                alignSelf: 'center',
                marginTop: '30%',
              }}>
              <LottieView
                style={{flex: 1}}
                source={no_data_loader}
                autoPlay
                loop
              />
            </View>
          )}
        </View>
      )}
      <Toast />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 60,
    // backgroundColor: colors.theme_bg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  segment_active_bg: {
    width: '33%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    backgroundColor: colors.theme_bg,
    borderRadius: 10,
  },
  segment_active_fg: {
    color: colors.theme_fg_two,
    fontSize: f_xs,
    fontFamily: normal,
    color: colors.theme_fg_three,
  },
  segment_inactive_bg: {
    width: '34%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    backgroundColor: colors.theme_bg_three,
    borderRadius: 10,
  },
  segment_inactive_fg: {
    color: colors.theme_fg_two,
    fontSize: f_xs,
    fontFamily: normal,
    color: colors.theme_fg_two,
  },
  driverInfoRow: {
    flexDirection: 'row',
    backgroundColor: colors.theme_bg_three,
    padding: 15,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  avatarContainer: {width: '17%', justifyContent: 'center'},
  avatarImage: {width: 50, height: 50, borderRadius: 10},
  nameRatingContainer: {width: '33%', justifyContent: 'center'},
  driverNameText: {
    color: colors.theme_fg_two,
    fontSize: f_s,
    fontFamily: bold,
  },
  ratingContainer: {flexDirection: 'row', alignItems: 'center'},
  ratingText: {
    color: colors.theme_bg_two,
    fontSize: f_s,
    fontFamily: bold,
    marginLeft: 3,
  },
  fareContainer: {
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  distanceContainer: {
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionLabel: {
    color: colors.text_grey,
    fontSize: f_xs,
    fontFamily: normal,
  },
  sectionValue: {
    fontSize: f_s,
    fontFamily: bold,
    color: colors.theme_fg_two,
    marginTop: 3,
  },
  divider: {
    borderBottomWidth: 0.5,
    borderColor: colors.grey,
  },
  addressContainer: {
    backgroundColor: colors.theme_bg_three,
    padding: 15,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  addressRow: {flexDirection: 'row', alignItems: 'center'},
  addressText: {
    color: colors.theme_fg_two,
    fontSize: f_xs,
    fontFamily: normal,
    marginLeft: 5,
  },
  dottedLine: {
    height: 20,
    borderLeftWidth: 1,
    marginLeft: 3,
    borderStyle: 'dotted',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  dateTimeText: {
    fontSize: f_tiny,
    fontFamily: normal,
    color: colors.text_grey,
  },
  cancellationOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
});

export default MyRides;