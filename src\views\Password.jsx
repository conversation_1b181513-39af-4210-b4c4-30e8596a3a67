import { useTranslation } from "react-i18next";
import '../languages/i18next';

import React, { useState, useEffect, useRef } from "react";
import {
    TouchableOpacity,
    Text,
    StyleSheet,
    View,
    TextInput,
    StatusBar
} from "react-native";
import { useNavigation, useRoute, CommonActions } from "@react-navigation/native";
import * as colors from '../assets/css/Colors';
import { api_url, normal, bold, regular, login, btn_loader, f_xl, f_xs, f_m } from '../config/Constants';
import Icon, { Icons } from '../components/Icons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LottieView from 'lottie-react-native';
import { updateFirstName, updateLastName, updateEmail } from '../actions/RegisterActions';
import { connect } from 'react-redux';
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const Password = (props) => {
  const { t, i18n } = useTranslation();
    const navigation = useNavigation();
    const route = useRoute();
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [phone_number, setPhoneNumber] = useState(route.params.phone_number);
    const [secureTextEntry, setSecureTextEntry] = useState(true);

    const inputRef = useRef();

    const go_back = () => {
        navigation.goBack();
    }

    useEffect(() => {
        setTimeout(() => inputRef.current.focus(), 100)
    }, []);


    const check_valid = () => {
        if (password) {
            call_login();
        } else {
           showToast(
             "error",
             t('Validation error'),
             t('Please enter your password')
           );
           
        }
    }
  const showToast = (type, title, message) => {
ReactNativeHapticFeedback.trigger("impactHeavy", options);

    Toast.show({
      type: type ,
      text1: title,
      text2: message,
      visibilityTime: 5000,
      position: "top", // top, bottom
    });
  };
    const call_login = async () => {
        console.log({ phone_with_code: phone_number, password: password, fcm_token: global.fcm_token,})
        setLoading(true);
        await axios({
            method: 'post',
            url: api_url + login,
            data: { phone_with_code: phone_number, password: password, fcm_token: global.fcm_token }
        })
            .then(async response => {
                console.log(response.data)
                           setLoading(false);

                save_data(response.data.result, response.data.status, response.data.message);
            })
            .catch(error => {
                setLoading(false);
               showToast(
                 "error",
                 t('Error'),
                 t('Sorry something went wrong')
               );
               
            });
    }

    const navigate = async (data) => {
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: "Home" }],
            })
        );
    }

    const save_data = async (data, status, message) => {
                        setLoading(true);

        if (status == 1) {
            try {
                await AsyncStorage.setItem('id', data.id.toString());
                await AsyncStorage.setItem('first_name', data.first_name.toString());
                await AsyncStorage.setItem('profile_picture', data.profile_picture.toString());
                await AsyncStorage.setItem('phone_with_code', data.phone_with_code.toString());
                await AsyncStorage.setItem('email', data.email.toString());
                global.id = await data.id;
                global.first_name = await data.first_name;
                global.phone_with_code = await data.phone_with_code;
                global.email = await data.email;
                global.profile_picture = await data.profile_picture;
                props.updateFirstName(data.first_name);
                props.updateLastName(data.last_name);
                props.updateEmail(data.email);
                setLoading(false);
                await navigate();
            } catch (e) {
                setLoading(false);
                showToast(
                  "error",
                  t('Error'),
                  t('Sorry something went wrong')
                );
             
            }
        } else {
            setLoading(false);
            console.log(message)
            if (message == "Invalid phone number or password"){
              showToast(
                "error",
                t('Error'),
                t('Invalid phone number or password')
              );
             
            }
            else{
              showToast("error", t('Error'), message);
           
            }
        }
    }

    const forgot_password = () => {
        navigation.navigate('Forgot');
    }


    return (
      <SafeAreaView style={{}}>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle="dark-content"
        />

        <View style={[styles.header]}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={go_back.bind(this)}
            style={{
              width: "15%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon
              type={Icons.MaterialIcons}
              name="arrow-back"
              color={colors.theme_fg_two}
              style={{ fontSize: 30 }}
            />
          </TouchableOpacity>
        </View>
        <View style={{ margin: 20 }} />
        <View style={{ alignItems: "center", justifyContent: "center" }}>
          <Text
            numberOfLines={1}
            style={{
              color: colors.theme_fg_two,
              fontSize: f_xl,
              fontFamily: regular,
            }}
          >
            {t('Enter your password')}
          </Text>
          <View style={{ margin: 5 }} />
          <Text
            numberOfLines={1}
            style={{ color: colors.grey, fontSize: f_xs, fontFamily: normal }}
          >
            {t('You need enter your password')}
          </Text>
          <View style={{ margin: 20 }} />
          <View style={{ width: "80%" }}>
            <View style={{ flexDirection: "row" }}>
              <View
                style={{
                  width: "20%",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: colors.theme_bg_three,
                }}
              >
                <Icon
                  type={Icons.MaterialIcons}
                  name="lock"
                  color={colors.theme_fg_two}
                  style={{ fontSize: 30 }}
                />
              </View>
              <View
                style={{
                  width: "60%",
                  alignItems: "flex-start",
                  paddingLeft: 10,
                  justifyContent: "center",
                  backgroundColor: colors.text_container_bg,
                }}
              >
                <TextInput
                  ref={inputRef}
                  secureTextEntry={secureTextEntry}
                  placeholder={t('Password')}
                  placeholderTextColor={colors.grey}
                  style={styles.textinput}
                  onChangeText={(TextInputValue) => setPassword(TextInputValue)}
                />
              </View>
                <TouchableOpacity
                  onPress={() => setSecureTextEntry(!secureTextEntry)}
                  style={{width:'20%',justifyContent:'center',alignItems:'center',backgroundColor:colors.text_container_bg}}
                >
                  <Icon
                    type={Icons.MaterialIcons}
                    name={secureTextEntry ? "visibility-off" : "visibility"}
                    color={colors.grey}
                    style={{ fontSize: 25, marginRight: 10 }}
                  />
                </TouchableOpacity>
            </View>
            <View style={{ margin: 10 }} />
            <Text
              onPress={forgot_password.bind(this)}
              numberOfLines={1}
              style={{
                color: colors.grey,
                fontSize: f_xs,
                fontFamily: normal,
                textAlign: "right",
              }}
            >
              {t('Forgot Password')}?
            </Text>
            <View style={{ margin: 30 }} />

            {loading == false ? (
              <TouchableOpacity
                onPress={check_valid.bind(this)}
                activeOpacity={1}
                style={{
                  width: "100%",
                  backgroundColor: colors.btn_color,
                  borderRadius: 10,
                  height: 50,
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    color: colors.theme_fg_two,
                    fontSize: f_m,
                    color: colors.theme_fg_three,
                    fontFamily: normal,
                  }}
                >
                  {t('Login')}
                </Text>
              </TouchableOpacity>
            ) : (
              <View style={{ height: 50, width: "90%", alignSelf: "center" }}>
                <LottieView
                  style={{ flex: 1 }}
                  source={btn_loader}
                  autoPlay
                  loop
                />
              </View>
            )}
          </View>
        </View>
        <Toast />
      </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    header: {
        height: 60,
        backgroundColor: colors.lite_bg,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textinput: {
        fontSize: f_m,
        color: colors.grey,
        fontFamily: regular,
        height: 60,
        backgroundColor: colors.text_container_bg,
        width: '100%'
    },
});

const mapDispatchToProps = (dispatch) => ({
    updateEmail: (data) => dispatch(updateEmail(data)),
    updateFirstName: (data) => dispatch(updateFirstName(data)),
    updateLastName: (data) => dispatch(updateLastName(data)),
});

export default connect(null, mapDispatchToProps)(Password);